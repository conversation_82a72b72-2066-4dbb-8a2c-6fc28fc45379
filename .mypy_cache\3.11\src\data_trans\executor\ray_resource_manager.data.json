{".class": "MypyFile", "_fullname": "src.data_trans.executor.ray_resource_manager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AutoScalingConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig", "name": "AutoScalingConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"decision_window": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 103, "name": "decision_window", "strict": null, "type": "builtins.int"}, "enable_autoscaling": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 89, "name": "enable_autoscaling", "strict": null, "type": "builtins.bool"}, "max_nodes": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 94, "name": "max_nodes", "strict": null, "type": "builtins.int"}, "min_nodes": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 99, "name": "min_nodes", "strict": null, "type": "builtins.int"}, "monitoring_interval": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 102, "name": "monitoring_interval", "strict": null, "type": "builtins.int"}, "scale_down_cooldown": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 98, "name": "scale_down_cooldown", "strict": null, "type": "builtins.int"}, "scale_down_threshold": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 97, "name": "scale_down_threshold", "strict": null, "type": "builtins.float"}, "scale_up_cooldown": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 93, "name": "scale_up_cooldown", "strict": null, "type": "builtins.int"}, "scale_up_threshold": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 92, "name": "scale_up_threshold", "strict": null, "type": "builtins.float"}}}}, "module_name": "src.data_trans.executor.ray_resource_manager", "mro": ["src.data_trans.executor.ray_resource_manager.AutoScalingConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "enable_autoscaling", "scale_up_threshold", "scale_up_cooldown", "max_nodes", "scale_down_threshold", "scale_down_cooldown", "min_nodes", "monitoring_interval", "decision_window"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "enable_autoscaling", "scale_up_threshold", "scale_up_cooldown", "max_nodes", "scale_down_threshold", "scale_down_cooldown", "min_nodes", "monitoring_interval", "decision_window"], "arg_types": ["src.data_trans.executor.ray_resource_manager.AutoScalingConfig", "builtins.bool", "builtins.float", "builtins.int", "builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AutoScalingConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "decision_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.decision_window", "name": "decision_window", "setter_type": null, "type": "builtins.int"}}, "enable_autoscaling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.enable_autoscaling", "name": "enable_autoscaling", "setter_type": null, "type": "builtins.bool"}}, "max_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.max_nodes", "name": "max_nodes", "setter_type": null, "type": "builtins.int"}}, "min_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.min_nodes", "name": "min_nodes", "setter_type": null, "type": "builtins.int"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "enable_autoscaling", "scale_up_threshold", "scale_up_cooldown", "max_nodes", "scale_down_threshold", "scale_down_cooldown", "min_nodes", "monitoring_interval", "decision_window"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "enable_autoscaling", "scale_up_threshold", "scale_up_cooldown", "max_nodes", "scale_down_threshold", "scale_down_cooldown", "min_nodes", "monitoring_interval", "decision_window"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.int", "builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of AutoScalingConfig", "ret_type": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "enable_autoscaling", "scale_up_threshold", "scale_up_cooldown", "max_nodes", "scale_down_threshold", "scale_down_cooldown", "min_nodes", "monitoring_interval", "decision_window"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.int", "builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of AutoScalingConfig", "ret_type": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "monitoring_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.monitoring_interval", "name": "monitoring_interval", "setter_type": null, "type": "builtins.int"}}, "scale_down_cooldown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.scale_down_cooldown", "name": "scale_down_cooldown", "setter_type": null, "type": "builtins.int"}}, "scale_down_threshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.scale_down_threshold", "name": "scale_down_threshold", "setter_type": null, "type": "builtins.float"}}, "scale_up_cooldown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.scale_up_cooldown", "name": "scale_up_cooldown", "setter_type": null, "type": "builtins.int"}}, "scale_up_threshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.scale_up_threshold", "name": "scale_up_threshold", "setter_type": null, "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RAY_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.RAY_AVAILABLE", "name": "RAY_AVAILABLE", "setter_type": null, "type": "builtins.bool"}}, "RayResourceManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager", "name": "RayResourceManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.data_trans.executor.ray_resource_manager", "mro": ["src.data_trans.executor.ray_resource_manager.RayResourceManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "allocation_config", "autoscaling_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "allocation_config", "autoscaling_config"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager", {".class": "UnionType", "items": ["src.data_trans.executor.ray_resource_manager.ResourceAllocation", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["src.data_trans.executor.ray_resource_manager.AutoScalingConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayResourceManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_autoscaling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "current_usage"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager._check_autoscaling", "name": "_check_autoscaling", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "current_usage"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager", "src.data_trans.executor.ray_resource_manager.ResourceUsage"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_autoscaling of RayResourceManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_recommendations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "current_usage", "requested_tasks", "max_concurrent_tasks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager._generate_recommendations", "name": "_generate_recommendations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "current_usage", "requested_tasks", "max_concurrent_tasks"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager", "src.data_trans.executor.ray_resource_manager.ResourceUsage", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_generate_recommendations of RayResourceManager", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_bottleneck": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "max_cpu", "max_memory", "max_gpu"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager._identify_bottleneck", "name": "_identify_bottleneck", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "max_cpu", "max_memory", "max_gpu"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_identify_bottleneck of RayResourceManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_monitoring_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager._monitoring_loop", "name": "_monitoring_loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_monitoring_loop of RayResourceManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scale_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager._scale_down", "name": "_scale_down", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_scale_down of RayResourceManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scale_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager._scale_up", "name": "_scale_up", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_scale_up of RayResourceManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allocation_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.allocation_config", "name": "allocation_config", "setter_type": null, "type": "src.data_trans.executor.ray_resource_manager.ResourceAllocation"}}, "autoscaling_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.autoscaling_config", "name": "autoscaling_config", "setter_type": null, "type": "src.data_trans.executor.ray_resource_manager.AutoScalingConfig"}}, "calculate_optimal_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_count", "task_requirements"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.calculate_optimal_allocation", "name": "calculate_optimal_allocation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_count", "task_requirements"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "calculate_optimal_allocation of RayResourceManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_current_usage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.get_current_usage", "name": "get_current_usage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_current_usage of RayResourceManager", "ret_type": {".class": "UnionType", "items": ["src.data_trans.executor.ray_resource_manager.ResourceUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_usage_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "hours"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.get_usage_statistics", "name": "get_usage_statistics", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "hours"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_usage_statistics of RayResourceManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_monitoring": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.is_monitoring", "name": "is_monitoring", "setter_type": null, "type": "builtins.bool"}}, "last_scale_down_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.last_scale_down_time", "name": "last_scale_down_time", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "last_scale_up_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.last_scale_up_time", "name": "last_scale_up_time", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_history_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.max_history_size", "name": "max_history_size", "setter_type": null, "type": "builtins.int"}}, "monitoring_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.monitoring_task", "name": "monitoring_task", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "start_monitoring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.start_monitoring", "name": "start_monitoring", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "start_monitoring of RayResourceManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_monitoring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.stop_monitoring", "name": "stop_monitoring", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_resource_manager.RayResourceManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stop_monitoring of RayResourceManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "usage_history": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.usage_history", "name": "usage_history", "setter_type": null, "type": {".class": "Instance", "args": ["src.data_trans.executor.ray_resource_manager.ResourceUsage"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_resource_manager.RayResourceManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_resource_manager.RayResourceManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceAllocation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation", "name": "ResourceAllocation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"cpu_overcommit_ratio": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 67, "name": "cpu_overcommit_ratio", "strict": null, "type": "builtins.float"}, "cpu_per_task": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 66, "name": "cpu_per_task", "strict": null, "type": "builtins.float"}, "enable_load_balancing": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 81, "name": "enable_load_balancing", "strict": null, "type": "builtins.bool"}, "gpu_per_task": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 74, "name": "gpu_per_task", "strict": null, "type": "builtins.float"}, "load_balance_threshold": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 82, "name": "load_balance_threshold", "strict": null, "type": "builtins.float"}, "memory_overcommit_ratio": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 71, "name": "memory_overcommit_ratio", "strict": null, "type": "builtins.float"}, "memory_per_task": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 70, "name": "memory_per_task", "strict": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "placement_group_strategy": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 78, "name": "placement_group_strategy", "strict": null, "type": "builtins.str"}, "scheduling_strategy": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 77, "name": "scheduling_strategy", "strict": null, "type": "builtins.str"}}}}, "module_name": "src.data_trans.executor.ray_resource_manager", "mro": ["src.data_trans.executor.ray_resource_manager.ResourceAllocation", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "cpu_per_task", "cpu_overcommit_ratio", "memory_per_task", "memory_overcommit_ratio", "gpu_per_task", "scheduling_strategy", "placement_group_strategy", "enable_load_balancing", "load_balance_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "cpu_per_task", "cpu_overcommit_ratio", "memory_per_task", "memory_overcommit_ratio", "gpu_per_task", "scheduling_strategy", "placement_group_strategy", "enable_load_balancing", "load_balance_threshold"], "arg_types": ["src.data_trans.executor.ray_resource_manager.ResourceAllocation", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.str", "builtins.str", "builtins.bool", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResourceAllocation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "cpu_overcommit_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.cpu_overcommit_ratio", "name": "cpu_overcommit_ratio", "setter_type": null, "type": "builtins.float"}}, "cpu_per_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.cpu_per_task", "name": "cpu_per_task", "setter_type": null, "type": "builtins.float"}}, "enable_load_balancing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.enable_load_balancing", "name": "enable_load_balancing", "setter_type": null, "type": "builtins.bool"}}, "gpu_per_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.gpu_per_task", "name": "gpu_per_task", "setter_type": null, "type": "builtins.float"}}, "load_balance_threshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.load_balance_threshold", "name": "load_balance_threshold", "setter_type": null, "type": "builtins.float"}}, "memory_overcommit_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.memory_overcommit_ratio", "name": "memory_overcommit_ratio", "setter_type": null, "type": "builtins.float"}}, "memory_per_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.memory_per_task", "name": "memory_per_task", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "cpu_per_task", "cpu_overcommit_ratio", "memory_per_task", "memory_overcommit_ratio", "gpu_per_task", "scheduling_strategy", "placement_group_strategy", "enable_load_balancing", "load_balance_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "cpu_per_task", "cpu_overcommit_ratio", "memory_per_task", "memory_overcommit_ratio", "gpu_per_task", "scheduling_strategy", "placement_group_strategy", "enable_load_balancing", "load_balance_threshold"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_resource_manager.ResourceAllocation"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.str", "builtins.str", "builtins.bool", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of ResourceAllocation", "ret_type": "src.data_trans.executor.ray_resource_manager.ResourceAllocation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "cpu_per_task", "cpu_overcommit_ratio", "memory_per_task", "memory_overcommit_ratio", "gpu_per_task", "scheduling_strategy", "placement_group_strategy", "enable_load_balancing", "load_balance_threshold"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_resource_manager.ResourceAllocation"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.str", "builtins.str", "builtins.bool", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of ResourceAllocation", "ret_type": "src.data_trans.executor.ray_resource_manager.ResourceAllocation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "placement_group_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.placement_group_strategy", "name": "placement_group_strategy", "setter_type": null, "type": "builtins.str"}}, "scheduling_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.scheduling_strategy", "name": "scheduling_strategy", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_resource_manager.ResourceAllocation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_resource_manager.ResourceAllocation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceUsage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage", "name": "ResourceUsage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"active_nodes": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 55, "name": "active_nodes", "strict": null, "type": "builtins.int"}, "available_cpu": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 38, "name": "available_cpu", "strict": null, "type": "builtins.float"}, "available_gpu": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 50, "name": "available_gpu", "strict": null, "type": "builtins.float"}, "available_memory": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 44, "name": "available_memory", "strict": null, "type": "builtins.int"}, "cpu_utilization": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 39, "name": "cpu_utilization", "strict": null, "type": "builtins.float"}, "gpu_utilization": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 51, "name": "gpu_utilization", "strict": null, "type": "builtins.float"}, "memory_utilization": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 45, "name": "memory_utilization", "strict": null, "type": "builtins.float"}, "node_count": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 54, "name": "node_count", "strict": null, "type": "builtins.int"}, "pending_tasks": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 59, "name": "pending_tasks", "strict": null, "type": "builtins.int"}, "running_tasks": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 58, "name": "running_tasks", "strict": null, "type": "builtins.int"}, "timestamp": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 33, "name": "timestamp", "strict": null, "type": "datetime.datetime"}, "total_cpu": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 36, "name": "total_cpu", "strict": null, "type": "builtins.float"}, "total_gpu": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 48, "name": "total_gpu", "strict": null, "type": "builtins.float"}, "total_memory": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 42, "name": "total_memory", "strict": null, "type": "builtins.int"}, "used_cpu": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 37, "name": "used_cpu", "strict": null, "type": "builtins.float"}, "used_gpu": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 49, "name": "used_gpu", "strict": null, "type": "builtins.float"}, "used_memory": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 43, "name": "used_memory", "strict": null, "type": "builtins.int"}}}}, "module_name": "src.data_trans.executor.ray_resource_manager", "mro": ["src.data_trans.executor.ray_resource_manager.ResourceUsage", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5, 3, 3, 5, 5], "arg_names": ["__pydantic_self__", "timestamp", "total_cpu", "used_cpu", "available_cpu", "cpu_utilization", "total_memory", "used_memory", "available_memory", "memory_utilization", "total_gpu", "used_gpu", "available_gpu", "gpu_utilization", "node_count", "active_nodes", "running_tasks", "pending_tasks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5, 3, 3, 5, 5], "arg_names": ["__pydantic_self__", "timestamp", "total_cpu", "used_cpu", "available_cpu", "cpu_utilization", "total_memory", "used_memory", "available_memory", "memory_utilization", "total_gpu", "used_gpu", "available_gpu", "gpu_utilization", "node_count", "active_nodes", "running_tasks", "pending_tasks"], "arg_types": ["src.data_trans.executor.ray_resource_manager.ResourceUsage", "datetime.datetime", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResourceUsage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "active_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.active_nodes", "name": "active_nodes", "setter_type": null, "type": "builtins.int"}}, "available_cpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.available_cpu", "name": "available_cpu", "setter_type": null, "type": "builtins.float"}}, "available_gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.available_gpu", "name": "available_gpu", "setter_type": null, "type": "builtins.float"}}, "available_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.available_memory", "name": "available_memory", "setter_type": null, "type": "builtins.int"}}, "cpu_utilization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.cpu_utilization", "name": "cpu_utilization", "setter_type": null, "type": "builtins.float"}}, "gpu_utilization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.gpu_utilization", "name": "gpu_utilization", "setter_type": null, "type": "builtins.float"}}, "memory_utilization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.memory_utilization", "name": "memory_utilization", "setter_type": null, "type": "builtins.float"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 3, 3, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5, 3, 3, 5, 5], "arg_names": [null, "_fields_set", "timestamp", "total_cpu", "used_cpu", "available_cpu", "cpu_utilization", "total_memory", "used_memory", "available_memory", "memory_utilization", "total_gpu", "used_gpu", "available_gpu", "gpu_utilization", "node_count", "active_nodes", "running_tasks", "pending_tasks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 3, 3, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5, 3, 3, 5, 5], "arg_names": ["_cls", "_fields_set", "timestamp", "total_cpu", "used_cpu", "available_cpu", "cpu_utilization", "total_memory", "used_memory", "available_memory", "memory_utilization", "total_gpu", "used_gpu", "available_gpu", "gpu_utilization", "node_count", "active_nodes", "running_tasks", "pending_tasks"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_resource_manager.ResourceUsage"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "datetime.datetime", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of ResourceUsage", "ret_type": "src.data_trans.executor.ray_resource_manager.ResourceUsage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 3, 3, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5, 3, 3, 5, 5], "arg_names": ["_cls", "_fields_set", "timestamp", "total_cpu", "used_cpu", "available_cpu", "cpu_utilization", "total_memory", "used_memory", "available_memory", "memory_utilization", "total_gpu", "used_gpu", "available_gpu", "gpu_utilization", "node_count", "active_nodes", "running_tasks", "pending_tasks"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_resource_manager.ResourceUsage"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "datetime.datetime", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of ResourceUsage", "ret_type": "src.data_trans.executor.ray_resource_manager.ResourceUsage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "node_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.node_count", "name": "node_count", "setter_type": null, "type": "builtins.int"}}, "pending_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.pending_tasks", "name": "pending_tasks", "setter_type": null, "type": "builtins.int"}}, "running_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.running_tasks", "name": "running_tasks", "setter_type": null, "type": "builtins.int"}}, "timestamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.timestamp", "name": "timestamp", "setter_type": null, "type": "datetime.datetime"}}, "total_cpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.total_cpu", "name": "total_cpu", "setter_type": null, "type": "builtins.float"}}, "total_gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.total_gpu", "name": "total_gpu", "setter_type": null, "type": "builtins.float"}}, "total_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.total_memory", "name": "total_memory", "setter_type": null, "type": "builtins.int"}}, "used_cpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.used_cpu", "name": "used_cpu", "setter_type": null, "type": "builtins.float"}}, "used_gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.used_gpu", "name": "used_gpu", "setter_type": null, "type": "builtins.float"}}, "used_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.used_memory", "name": "used_memory", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_resource_manager.ResourceUsage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_resource_manager.ResourceUsage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_resource_manager.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_resource_manager.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_resource_manager.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_resource_manager.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_resource_manager.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_resource_manager.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_ray_config_manager": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.ray_config.get_ray_config_manager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_resource_manager.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_resource_manager.structlog", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_resource_manager.structlog", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ray": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.ray", "name": "ray", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_resource_manager.ray", "source_any": null, "type_of_any": 3}}}, "structlog": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_resource_manager.structlog", "name": "structlog", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_resource_manager.structlog", "source_any": null, "type_of_any": 3}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "src\\data_trans\\executor\\ray_resource_manager.py"}