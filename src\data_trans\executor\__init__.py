"""
数据采集任务执行器模块

该模块实现完整的数据采集到存储流程，整合爬虫、清洗、存储功能。
"""

__version__ = "0.1.0"
__author__ = "Data Trans Team"

# 导入主要的执行器类
from .base_executor import BaseExecutor, ExecutorConfig

# 导入测试和验证模块
from .python311_compatibility import (
    PerformanceBenchmark,
    Python311Features,
    Python311Validator,
)

# 导入Ray相关模块
from .ray_actors import ResourceMonitorActor, TaskManagerActor
from .ray_cluster_manager import (
    ClusterHealth,
    ClusterStatus,
    NodeStatus,
    RayClusterManager,
)
from .ray_config import (
    RayActorResourceConfig,
    RayClusterConfig,
    RayConfigManager,
    RayPerformanceConfig,
    RayTaskResourceConfig,
    get_ray_config_manager,
)
from .ray_executor import RayExecutor, RayExecutorConfig, RayTaskResult
from .ray_integration_test import RayIntegrationTestSuite
from .ray_resource_manager import (
    AutoScalingConfig,
    RayResourceManager,
    ResourceAllocation,
    ResourceUsage,
)
from .ray_state_manager import (
    DistributedStateActor,
    RayStateManager,
    StateEntry,
    StateSnapshot,
)
from .ray_task_executor import RayTaskExecutor, RayTaskExecutorConfig
from .task_executor import TaskExecutor, TaskExecutorConfig

__all__: list[str] = [
    "BaseExecutor",
    "ExecutorConfig",
    "TaskExecutor",
    "TaskExecutorConfig",
    "RayExecutor",
    "RayExecutorConfig",
    "RayTaskResult",
    "RayTaskExecutor",
    "RayTaskExecutorConfig",
    "TaskManagerActor",
    "ResourceMonitorActor",
    "RayClusterConfig",
    "RayTaskResourceConfig",
    "RayActorResourceConfig",
    "RayPerformanceConfig",
    "RayConfigManager",
    "get_ray_config_manager",
    "RayResourceManager",
    "ResourceUsage",
    "ResourceAllocation",
    "AutoScalingConfig",
    "RayClusterManager",
    "ClusterStatus",
    "NodeStatus",
    "ClusterHealth",
    "RayStateManager",
    "DistributedStateActor",
    "StateEntry",
    "StateSnapshot",
    "Python311Validator",
    "Python311Features",
    "PerformanceBenchmark",
    "RayIntegrationTestSuite",
]
