{"data_mtime": 1752392499, "dep_lines": [15, 16, 14, 10, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["src.data_trans.executor.base_executor", "src.data_trans.executor.task_executor", "src.data_trans.executor", "unittest.mock", "asyncio", "json", "datetime", "builtins", "_frozen_importlib", "abc", "enum", "src", "typing", "typing_extensions", "unittest"], "hash": "1b08943b694135e1be80b7b03b06783913238038", "id": "tests.test_task_executor", "ignore_all": false, "interface_hash": "2d47e86586a3915fe417136863371e1416abcea8", "mtime": 1752392497, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "tests\\test_task_executor.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 9850, "suppressed": ["pytest"], "version_id": "1.16.1"}