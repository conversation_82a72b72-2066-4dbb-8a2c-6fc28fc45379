{"data_mtime": 1752392499, "dep_lines": [11, 14, 21, 22, 28, 36, 37, 38, 44, 50, 51, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["src.data_trans.executor.base_executor", "src.data_trans.executor.python311_compatibility", "src.data_trans.executor.ray_actors", "src.data_trans.executor.ray_cluster_manager", "src.data_trans.executor.ray_config", "src.data_trans.executor.ray_executor", "src.data_trans.executor.ray_integration_test", "src.data_trans.executor.ray_resource_manager", "src.data_trans.executor.ray_state_manager", "src.data_trans.executor.ray_task_executor", "src.data_trans.executor.task_executor", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "3ca16e6b6a6b5be3632ee2015f180852a223531e", "id": "src.data_trans.executor", "ignore_all": false, "interface_hash": "34cd92b1c193742ffba1bba4ef5a98c5688e5723", "mtime": 1752392497, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src\\data_trans\\executor\\__init__.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 2144, "suppressed": [], "version_id": "1.16.1"}