"""
数据采集任务执行器实现

整合爬虫、清洗、存储功能，实现完整的数据采集到存储流程。
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Union

import structlog
from pydantic import BaseModel, Field

from ..cleaners.base_cleaner import BaseCleaner, CleanerConfig
from ..cleaners.text_cleaner import TextCleaner, TextCleanerConfig
from ..config.settings import get_settings
from ..crawlers.api_crawler import APICrawler
from ..crawlers.base_crawler import BaseCrawler, CrawlerConfig
from ..crawlers.web_crawler import WebCrawler
from ..storage.base_storage import BaseStorage
from ..storage.mongodb_storage import MongoDBConfig, MongoDBStorage
from ..storage.redis_storage import RedisConfig, RedisStorage
from .base_executor import (
    BaseExecutor,
    ExecutionResult,
    ExecutionStatus,
    ExecutorConfig,
)

# 配置结构化日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer(),
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class TaskExecutorConfig(ExecutorConfig):
    """任务执行器配置"""

    # 存储配置
    default_storage_type: str = Field(default="mongodb", description="默认存储类型")
    raw_data_collection: str = Field(default="raw_data", description="原始数据集合名")
    cleaned_data_collection: str = Field(
        default="cleaned_data", description="清洗数据集合名"
    )

    # 清洗配置
    default_cleaner_type: str = Field(default="text", description="默认清洗器类型")

    # 钩子配置
    enable_before_crawl_hook: bool = Field(default=True, description="启用爬取前钩子")
    enable_after_crawl_hook: bool = Field(default=True, description="启用爬取后钩子")
    enable_before_clean_hook: bool = Field(default=True, description="启用清洗前钩子")
    enable_after_clean_hook: bool = Field(default=True, description="启用清洗后钩子")
    enable_before_store_hook: bool = Field(default=True, description="启用存储前钩子")
    enable_after_store_hook: bool = Field(default=True, description="启用存储后钩子")


class TaskConfig(BaseModel):
    """单个任务配置"""

    task_id: str = Field(description="任务ID")
    task_type: str = Field(default="crawl_clean_store", description="任务类型")

    # 爬虫配置
    crawler_type: str = Field(default="web", description="爬虫类型: web, api")
    crawler_config: Dict[str, Any] = Field(default_factory=dict, description="爬虫配置")
    urls: List[str] = Field(description="要爬取的URL列表")

    # 清洗配置
    cleaner_type: str = Field(default="text", description="清洗器类型")
    cleaner_config: Dict[str, Any] = Field(
        default_factory=dict, description="清洗器配置"
    )
    cleaning_rules: List[Dict[str, Any]] = Field(
        default_factory=list, description="清洗规则"
    )

    # 存储配置
    storage_type: str = Field(default="mongodb", description="存储类型")
    storage_config: Dict[str, Any] = Field(default_factory=dict, description="存储配置")
    raw_collection: Optional[str] = Field(default=None, description="原始数据集合")
    cleaned_collection: Optional[str] = Field(default=None, description="清洗数据集合")

    # 执行配置
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟")
    timeout: float = Field(default=300.0, description="超时时间")


class TaskExecutor(BaseExecutor):
    """数据采集任务执行器

    整合爬虫、清洗、存储功能，实现完整的数据采集到存储流程。
    """

    def __init__(self, config: TaskExecutorConfig) -> None:
        """初始化任务执行器

        Args:
            config: 执行器配置
        """
        super().__init__(config)
        self.config: TaskExecutorConfig = config
        self.settings = get_settings()

        # 存储实例缓存
        self._storage_cache: Dict[str, BaseStorage] = {}

        # 爬虫类型映射
        self._crawler_types: Dict[str, Type[BaseCrawler]] = {
            "web": WebCrawler,
            "api": APICrawler,
        }

        # 清洗器类型映射
        self._cleaner_types: Dict[str, Type[BaseCleaner]] = {
            "text": TextCleaner,
        }

        # 存储类型映射
        self._storage_types: Dict[str, Type[BaseStorage]] = {
            "mongodb": MongoDBStorage,
            "redis": RedisStorage,
        }

    async def execute_task(
        self, task_id: str, task_config: Dict[str, Any]
    ) -> ExecutionResult:
        """执行单个任务

        Args:
            task_id: 任务ID
            task_config: 任务配置

        Returns:
            执行结果
        """
        # 解析任务配置
        try:
            config = TaskConfig(task_id=task_id, **task_config)
        except Exception as e:
            result = ExecutionResult(
                task_id=task_id,
                status=ExecutionStatus.FAILED,
                start_time=datetime.utcnow(),
                error_message=f"任务配置解析失败: {e}",
            )
            result.complete()
            return result

        # 使用执行上下文管理器
        async with self.execution_context(task_id) as result:
            try:
                # 设置超时
                await asyncio.wait_for(
                    self._execute_task_internal(config, result), timeout=config.timeout
                )
            except asyncio.TimeoutError:
                result.status = ExecutionStatus.TIMEOUT
                result.set_error(f"任务执行超时 ({config.timeout}秒)")
            except Exception as e:
                result.set_error(f"任务执行异常: {e}")

        return result

    async def _execute_task_internal(
        self, config: TaskConfig, result: ExecutionResult
    ) -> None:
        """内部任务执行逻辑

        Args:
            config: 任务配置
            result: 执行结果对象
        """
        logger.info("开始执行任务", task_id=config.task_id, task_type=config.task_type)
        result.add_log(f"开始执行任务: {config.task_type}")

        # 1. 执行爬取前钩子
        if self.config.enable_before_crawl_hook:
            await self._execute_hook("before_crawl", config, result)

        # 2. 执行爬虫任务
        crawled_data = await self._execute_crawl(config, result)
        if not crawled_data:
            result.set_error("爬取数据为空")
            return

        # 3. 执行爬取后钩子
        if self.config.enable_after_crawl_hook:
            await self._execute_hook("after_crawl", config, result, data=crawled_data)

        # 4. 存储原始数据
        await self._store_raw_data(config, result, crawled_data)

        # 5. 执行清洗前钩子
        if self.config.enable_before_clean_hook:
            await self._execute_hook("before_clean", config, result, data=crawled_data)

        # 6. 执行数据清洗
        cleaned_data = await self._execute_clean(config, result, crawled_data)
        if not cleaned_data:
            result.add_log("清洗后数据为空，跳过存储")
            return

        # 7. 执行清洗后钩子
        if self.config.enable_after_clean_hook:
            await self._execute_hook("after_clean", config, result, data=cleaned_data)

        # 8. 执行存储前钩子
        if self.config.enable_before_store_hook:
            await self._execute_hook("before_store", config, result, data=cleaned_data)

        # 9. 存储清洗数据
        await self._store_cleaned_data(config, result, cleaned_data)

        # 10. 执行存储后钩子
        if self.config.enable_after_store_hook:
            await self._execute_hook("after_store", config, result, data=cleaned_data)

        logger.info(
            "任务执行完成",
            task_id=config.task_id,
            crawled=result.crawled_count,
            cleaned=result.cleaned_count,
            stored=result.stored_count,
        )
        result.add_log("任务执行完成")

    async def _execute_hook(
        self, hook_name: str, config: TaskConfig, result: ExecutionResult, **kwargs: Any
    ) -> None:
        """执行钩子函数

        Args:
            hook_name: 钩子名称
            config: 任务配置
            result: 执行结果
            **kwargs: 额外参数
        """
        try:
            logger.debug("执行钩子", hook=hook_name, task_id=config.task_id)
            result.add_log(f"执行钩子: {hook_name}")

            # 这里可以扩展自定义钩子逻辑
            # 目前只记录日志

        except Exception as e:
            logger.warning("钩子执行失败", hook=hook_name, error=str(e))
            result.add_log(f"钩子 {hook_name} 执行失败: {e}")

    async def _execute_crawl(
        self, config: TaskConfig, result: ExecutionResult
    ) -> List[Dict[str, Any]]:
        """执行爬虫任务

        Args:
            config: 任务配置
            result: 执行结果

        Returns:
            爬取的数据列表
        """
        logger.info("开始爬取数据", task_id=config.task_id, urls_count=len(config.urls))
        result.add_log(f"开始爬取 {len(config.urls)} 个URL")

        # 创建爬虫实例
        crawler_class = self._crawler_types.get(config.crawler_type)
        if not crawler_class:
            raise ValueError(f"不支持的爬虫类型: {config.crawler_type}")

        # 合并配置
        crawler_config_dict = {
            **self.settings.crawler.model_dump(),
            **config.crawler_config,
        }
        crawler_config = CrawlerConfig(**crawler_config_dict)
        crawler = crawler_class(crawler_config)

        crawled_data = []

        try:
            await crawler.setup()

            # 爬取所有URL
            for url in config.urls:
                try:
                    crawl_result = await crawler.fetch_single(url)
                    if crawl_result.success and crawl_result.data:
                        crawled_data.append(
                            {
                                "url": url,
                                "data": crawl_result.data,
                                "metadata": crawl_result.metadata,
                                "crawled_at": crawl_result.timestamp.isoformat(),
                            }
                        )
                        result.crawled_count += 1

                except Exception as e:
                    logger.warning("URL爬取失败", url=url, error=str(e))
                    result.add_log(f"URL爬取失败 {url}: {e}")

        finally:
            await crawler.cleanup()

        logger.info("爬取完成", task_id=config.task_id, count=len(crawled_data))
        result.add_log(f"爬取完成，获得 {len(crawled_data)} 条数据")

        return crawled_data

    async def _execute_clean(
        self,
        config: TaskConfig,
        result: ExecutionResult,
        raw_data: List[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """执行数据清洗

        Args:
            config: 任务配置
            result: 执行结果
            raw_data: 原始数据

        Returns:
            清洗后的数据列表
        """
        logger.info("开始数据清洗", task_id=config.task_id, count=len(raw_data))
        result.add_log(f"开始清洗 {len(raw_data)} 条数据")

        # 创建清洗器实例
        cleaner_class = self._cleaner_types.get(config.cleaner_type)
        if not cleaner_class:
            raise ValueError(f"不支持的清洗器类型: {config.cleaner_type}")

        # 合并配置
        cleaner_config_dict = config.cleaner_config.copy()

        # 根据清洗器类型创建配置
        if config.cleaner_type == "text":
            cleaner_config = TextCleanerConfig(**cleaner_config_dict)
        else:
            cleaner_config = CleanerConfig(**cleaner_config_dict)

        cleaner = cleaner_class(cleaner_config)

        # 添加清洗规则
        for rule_config in config.cleaning_rules:
            cleaner.add_rule(**rule_config)

        cleaned_data = []

        # 清洗每条数据
        for item in raw_data:
            try:
                # 提取数据部分进行清洗
                data_to_clean = item.get("data", {})
                if not isinstance(data_to_clean, dict):
                    continue

                clean_result = await cleaner.clean_single(data_to_clean)

                if clean_result.success:
                    cleaned_item = {
                        "original_url": item.get("url"),
                        "crawled_at": item.get("crawled_at"),
                        "cleaned_data": clean_result.cleaned_data,
                        "cleaning_metadata": {
                            "applied_rules": clean_result.applied_rules,
                            "warnings": clean_result.warnings,
                            "processing_time": clean_result.processing_time,
                        },
                        "cleaned_at": datetime.utcnow().isoformat(),
                    }
                    cleaned_data.append(cleaned_item)
                    result.cleaned_count += 1
                else:
                    logger.warning(
                        "数据清洗失败",
                        task_id=config.task_id,
                        errors=clean_result.errors,
                    )
                    result.add_log(f"数据清洗失败: {clean_result.errors}")

            except Exception as e:
                logger.warning("清洗单条数据异常", error=str(e))
                result.add_log(f"清洗数据异常: {e}")

        logger.info("数据清洗完成", task_id=config.task_id, count=len(cleaned_data))
        result.add_log(f"清洗完成，获得 {len(cleaned_data)} 条有效数据")

        return cleaned_data

    async def _store_raw_data(
        self,
        config: TaskConfig,
        result: ExecutionResult,
        raw_data: List[Dict[str, Any]],
    ) -> None:
        """存储原始数据

        Args:
            config: 任务配置
            result: 执行结果
            raw_data: 原始数据
        """
        if not raw_data:
            return

        logger.info("开始存储原始数据", task_id=config.task_id, count=len(raw_data))
        result.add_log(f"开始存储 {len(raw_data)} 条原始数据")

        storage = await self._get_storage(config.storage_type, config.storage_config)
        collection = config.raw_collection or self.config.raw_data_collection

        try:
            # 添加任务元数据
            data_with_metadata = []
            for item in raw_data:
                item_with_meta = {
                    **item,
                    "task_id": config.task_id,
                    "stored_at": datetime.utcnow().isoformat(),
                }
                data_with_metadata.append(item_with_meta)

            # 批量插入
            await storage.insert_many(collection, data_with_metadata)

            logger.info("原始数据存储完成", task_id=config.task_id, count=len(raw_data))
            result.add_log(f"原始数据存储完成: {len(raw_data)} 条")

        except Exception as e:
            logger.error("原始数据存储失败", task_id=config.task_id, error=str(e))
            result.add_log(f"原始数据存储失败: {e}")
            raise

    async def _store_cleaned_data(
        self,
        config: TaskConfig,
        result: ExecutionResult,
        cleaned_data: List[Dict[str, Any]],
    ) -> None:
        """存储清洗数据

        Args:
            config: 任务配置
            result: 执行结果
            cleaned_data: 清洗数据
        """
        if not cleaned_data:
            return

        logger.info("开始存储清洗数据", task_id=config.task_id, count=len(cleaned_data))
        result.add_log(f"开始存储 {len(cleaned_data)} 条清洗数据")

        storage = await self._get_storage(config.storage_type, config.storage_config)
        collection = config.cleaned_collection or self.config.cleaned_data_collection

        try:
            # 添加任务元数据
            data_with_metadata = []
            for item in cleaned_data:
                item_with_meta = {
                    **item,
                    "task_id": config.task_id,
                    "stored_at": datetime.utcnow().isoformat(),
                }
                data_with_metadata.append(item_with_meta)

            # 批量插入
            await storage.insert_many(collection, data_with_metadata)
            result.stored_count = len(cleaned_data)

            logger.info(
                "清洗数据存储完成", task_id=config.task_id, count=len(cleaned_data)
            )
            result.add_log(f"清洗数据存储完成: {len(cleaned_data)} 条")

        except Exception as e:
            logger.error("清洗数据存储失败", task_id=config.task_id, error=str(e))
            result.add_log(f"清洗数据存储失败: {e}")
            raise

    async def _get_storage(
        self, storage_type: str, storage_config: Dict[str, Any]
    ) -> BaseStorage:
        """获取存储实例

        Args:
            storage_type: 存储类型
            storage_config: 存储配置

        Returns:
            存储实例
        """
        cache_key = f"{storage_type}_{hash(json.dumps(storage_config, sort_keys=True))}"

        if cache_key in self._storage_cache:
            storage = self._storage_cache[cache_key]
            if storage.is_connected:
                return storage

        # 创建新的存储实例
        storage_class = self._storage_types.get(storage_type)
        if not storage_class:
            raise ValueError(f"不支持的存储类型: {storage_type}")

        # 合并默认配置
        if storage_type == "mongodb":
            default_config = {
                "host": self.settings.database.mongodb_host,
                "port": self.settings.database.mongodb_port,
                "username": self.settings.database.mongodb_user,
                "password": self.settings.database.mongodb_password,
                "database": self.settings.database.mongodb_database,
                "auth_source": self.settings.database.mongodb_auth_source,
            }
            merged_config = {**default_config, **storage_config}
            config_obj = MongoDBConfig(**merged_config)

        elif storage_type == "redis":
            default_config = {
                "host": self.settings.redis.host,
                "port": self.settings.redis.port,
                "password": self.settings.redis.password,
                "database": self.settings.redis.database,
                "max_connections": self.settings.redis.max_connections,
            }
            merged_config = {**default_config, **storage_config}
            config_obj = RedisConfig(**merged_config)

        else:
            raise ValueError(f"未配置的存储类型: {storage_type}")

        storage = storage_class(config_obj)
        await storage.connect()

        # 缓存存储实例
        self._storage_cache[cache_key] = storage

        return storage

    async def load_task_from_file(self, file_path: Union[str, Path]) -> TaskConfig:
        """从文件加载任务配置

        Args:
            file_path: 配置文件路径

        Returns:
            任务配置对象
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")

        # 根据文件扩展名解析
        if file_path.suffix.lower() == ".json":
            with open(file_path, "r", encoding="utf-8") as f:
                config_data = json.load(f)
        elif file_path.suffix.lower() in [".yaml", ".yml"]:
            import yaml

            with open(file_path, "r", encoding="utf-8") as f:
                config_data = yaml.safe_load(f)
        else:
            raise ValueError(f"不支持的配置文件格式: {file_path.suffix}")

        return TaskConfig(**config_data)

    async def execute_from_file(self, file_path: Union[str, Path]) -> ExecutionResult:
        """从配置文件执行任务

        Args:
            file_path: 配置文件路径

        Returns:
            执行结果
        """
        config = await self.load_task_from_file(file_path)
        return await self.execute_task(config.task_id, config.model_dump())

    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息

        Returns:
            统计信息字典
        """
        tasks = list(self._running_tasks.values())

        stats = {
            "total_tasks": len(tasks),
            "status_counts": {},
            "total_crawled": 0,
            "total_cleaned": 0,
            "total_stored": 0,
            "average_duration": 0.0,
            "success_rate": 0.0,
        }

        if not tasks:
            return stats

        # 统计状态分布
        for task in tasks:
            status = task.status.value
            stats["status_counts"][status] = stats["status_counts"].get(status, 0) + 1
            stats["total_crawled"] += task.crawled_count
            stats["total_cleaned"] += task.cleaned_count
            stats["total_stored"] += task.stored_count

        # 计算平均执行时间
        completed_tasks = [t for t in tasks if t.duration is not None]
        if completed_tasks:
            stats["average_duration"] = sum(t.duration for t in completed_tasks) / len(
                completed_tasks
            )

        # 计算成功率
        success_count = stats["status_counts"].get("success", 0)
        stats["success_rate"] = success_count / len(tasks) if tasks else 0.0

        return stats

    async def cleanup(self) -> None:
        """清理资源"""
        await super().cleanup()

        # 关闭所有存储连接
        for storage in self._storage_cache.values():
            try:
                await storage.disconnect()
            except Exception as e:
                logger.warning("存储连接关闭失败", error=str(e))

        self._storage_cache.clear()
        logger.info("任务执行器资源清理完成")
