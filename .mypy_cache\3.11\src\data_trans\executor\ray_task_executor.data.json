{".class": "MypyFile", "_fullname": "src.data_trans.executor.ray_task_executor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExecutionResult": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.base_executor.ExecutionResult", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExecutionStatus": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.base_executor.ExecutionStatus", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RAY_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RAY_AVAILABLE", "name": "RAY_AVAILABLE", "setter_type": null, "type": "builtins.bool"}}, "RayTaskExecutor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.data_trans.executor.task_executor.TaskExecutor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor", "name": "RayTaskExecutor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "src.data_trans.executor.ray_task_executor", "mro": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor", "src.data_trans.executor.task_executor.TaskExecutor", "src.data_trans.executor.base_executor.BaseExecutor", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor", "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayTaskExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_execute_batch_with_ray": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tasks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor._execute_batch_with_ray", "name": "_execute_batch_with_ray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tasks"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_execute_batch_with_ray of RayTaskExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["src.data_trans.executor.base_executor.ExecutionResult"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_execute_with_ray": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_id", "task_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor._execute_with_ray", "name": "_execute_with_ray", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_id", "task_config"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_execute_with_ray of RayTaskExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.base_executor.ExecutionResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cluster_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor._get_cluster_info", "name": "_get_cluster_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_cluster_info of RayTaskExecutor", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialize_ray": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor._initialize_ray", "name": "_initialize_ray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_initialize_ray of RayTaskExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ray_available": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor._ray_available", "name": "_ray_available", "setter_type": null, "type": "builtins.bool"}}, "_ray_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor._ray_initialized", "name": "_ray_initialized", "setter_type": null, "type": "builtins.bool"}}, "cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor.cleanup", "name": "cleanup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cleanup of RayTaskExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tasks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor.execute_batch", "name": "execute_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tasks"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute_batch of RayTaskExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["src.data_trans.executor.base_executor.ExecutionResult"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_id", "task_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor.execute_task", "name": "execute_task", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_id", "task_config"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute_task of RayTaskExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.base_executor.ExecutionResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_ray_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor.get_ray_status", "name": "get_ray_status", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_ray_status of RayTaskExecutor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ray_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor.ray_config", "name": "ray_config", "setter_type": null, "type": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_task_executor.RayTaskExecutor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RayTaskExecutorConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.data_trans.executor.task_executor.TaskExecutorConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig", "name": "RayTaskExecutorConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"cleaned_data_collection": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 57, "name": "cleaned_data_collection", "strict": null, "type": "builtins.str"}, "default_cleaner_type": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 60, "name": "default_cleaner_type", "strict": null, "type": "builtins.str"}, "default_storage_type": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 55, "name": "default_storage_type", "strict": null, "type": "builtins.str"}, "enable_after_clean_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 66, "name": "enable_after_clean_hook", "strict": null, "type": "builtins.bool"}, "enable_after_crawl_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 64, "name": "enable_after_crawl_hook", "strict": null, "type": "builtins.bool"}, "enable_after_store_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 68, "name": "enable_after_store_hook", "strict": null, "type": "builtins.bool"}, "enable_before_clean_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 65, "name": "enable_before_clean_hook", "strict": null, "type": "builtins.bool"}, "enable_before_crawl_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 63, "name": "enable_before_crawl_hook", "strict": null, "type": "builtins.bool"}, "enable_before_store_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 67, "name": "enable_before_store_hook", "strict": null, "type": "builtins.bool"}, "enable_hooks": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 96, "name": "enable_hooks", "strict": null, "type": "builtins.bool"}, "enable_metrics": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 93, "name": "enable_metrics", "strict": null, "type": "builtins.bool"}, "enable_ray_execution": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 42, "name": "enable_ray_execution", "strict": null, "type": "builtins.bool"}, "log_level": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 92, "name": "log_level", "strict": null, "type": "builtins.str"}, "max_concurrent_tasks": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 89, "name": "max_concurrent_tasks", "strict": null, "type": "builtins.int"}, "max_retries": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 84, "name": "max_retries", "strict": null, "type": "builtins.int"}, "raw_data_collection": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 56, "name": "raw_data_collection", "strict": null, "type": "builtins.str"}, "ray_batch_size": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 52, "name": "ray_batch_size", "strict": null, "type": "builtins.int"}, "ray_cpu_per_task": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 48, "name": "ray_cpu_per_task", "strict": null, "type": "builtins.float"}, "ray_enable_batching": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 53, "name": "ray_enable_batching", "strict": null, "type": "builtins.bool"}, "ray_fallback_to_local": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 43, "name": "ray_fallback_to_local", "strict": null, "type": "builtins.bool"}, "ray_max_retries": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 45, "name": "ray_max_retries", "strict": null, "type": "builtins.int"}, "ray_memory_per_task": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 49, "name": "ray_memory_per_task", "strict": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "ray_task_timeout": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 44, "name": "ray_task_timeout", "strict": null, "type": "builtins.float"}, "retry_delay": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 85, "name": "retry_delay", "strict": null, "type": "builtins.float"}, "timeout": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 86, "name": "timeout", "strict": null, "type": "builtins.float"}}}}, "module_name": "src.data_trans.executor.ray_task_executor", "mro": ["src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig", "src.data_trans.executor.task_executor.TaskExecutorConfig", "src.data_trans.executor.base_executor.ExecutorConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "enable_ray_execution", "ray_fallback_to_local", "ray_task_timeout", "ray_max_retries", "ray_cpu_per_task", "ray_memory_per_task", "ray_batch_size", "ray_enable_batching"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "enable_ray_execution", "ray_fallback_to_local", "ray_task_timeout", "ray_max_retries", "ray_cpu_per_task", "ray_memory_per_task", "ray_batch_size", "ray_enable_batching"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig", "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.float", "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayTaskExecutorConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "cleaned_data_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.cleaned_data_collection", "name": "cleaned_data_collection", "setter_type": null, "type": "builtins.str"}}, "default_cleaner_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.default_cleaner_type", "name": "default_cleaner_type", "setter_type": null, "type": "builtins.str"}}, "default_storage_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.default_storage_type", "name": "default_storage_type", "setter_type": null, "type": "builtins.str"}}, "enable_after_clean_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.enable_after_clean_hook", "name": "enable_after_clean_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_after_crawl_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.enable_after_crawl_hook", "name": "enable_after_crawl_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_after_store_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.enable_after_store_hook", "name": "enable_after_store_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_before_clean_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.enable_before_clean_hook", "name": "enable_before_clean_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_before_crawl_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.enable_before_crawl_hook", "name": "enable_before_crawl_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_before_store_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.enable_before_store_hook", "name": "enable_before_store_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_hooks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.enable_hooks", "name": "enable_hooks", "setter_type": null, "type": "builtins.bool"}}, "enable_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.enable_metrics", "name": "enable_metrics", "setter_type": null, "type": "builtins.bool"}}, "enable_ray_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.enable_ray_execution", "name": "enable_ray_execution", "setter_type": null, "type": "builtins.bool"}}, "log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.log_level", "name": "log_level", "setter_type": null, "type": "builtins.str"}}, "max_concurrent_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.max_concurrent_tasks", "name": "max_concurrent_tasks", "setter_type": null, "type": "builtins.int"}}, "max_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.max_retries", "name": "max_retries", "setter_type": null, "type": "builtins.int"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "enable_ray_execution", "ray_fallback_to_local", "ray_task_timeout", "ray_max_retries", "ray_cpu_per_task", "ray_memory_per_task", "ray_batch_size", "ray_enable_batching"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "enable_ray_execution", "ray_fallback_to_local", "ray_task_timeout", "ray_max_retries", "ray_cpu_per_task", "ray_memory_per_task", "ray_batch_size", "ray_enable_batching"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.float", "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayTaskExecutorConfig", "ret_type": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "enable_ray_execution", "ray_fallback_to_local", "ray_task_timeout", "ray_max_retries", "ray_cpu_per_task", "ray_memory_per_task", "ray_batch_size", "ray_enable_batching"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.float", "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayTaskExecutorConfig", "ret_type": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "raw_data_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.raw_data_collection", "name": "raw_data_collection", "setter_type": null, "type": "builtins.str"}}, "ray_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.ray_batch_size", "name": "ray_batch_size", "setter_type": null, "type": "builtins.int"}}, "ray_cpu_per_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.ray_cpu_per_task", "name": "ray_cpu_per_task", "setter_type": null, "type": "builtins.float"}}, "ray_enable_batching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.ray_enable_batching", "name": "ray_enable_batching", "setter_type": null, "type": "builtins.bool"}}, "ray_fallback_to_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.ray_fallback_to_local", "name": "ray_fallback_to_local", "setter_type": null, "type": "builtins.bool"}}, "ray_max_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.ray_max_retries", "name": "ray_max_retries", "setter_type": null, "type": "builtins.int"}}, "ray_memory_per_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.ray_memory_per_task", "name": "ray_memory_per_task", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ray_task_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.ray_task_timeout", "name": "ray_task_timeout", "setter_type": null, "type": "builtins.float"}}, "retry_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.retry_delay", "name": "retry_delay", "setter_type": null, "type": "builtins.float"}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.timeout", "name": "timeout", "setter_type": null, "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_task_executor.RayTaskExecutorConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RayTaskResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult", "name": "RayTaskResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"cleaned_count": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 66, "name": "cleaned_count", "strict": null, "type": "builtins.int"}, "crawled_count": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 65, "name": "crawled_count", "strict": null, "type": "builtins.int"}, "error_details": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 71, "name": "error_details", "strict": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, "error_message": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 70, "name": "error_message", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "execution_time": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 61, "name": "execution_time", "strict": null, "type": "builtins.float"}, "logs": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 74, "name": "logs", "strict": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, "metrics": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 75, "name": "metrics", "strict": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "stored_count": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 67, "name": "stored_count", "strict": null, "type": "builtins.int"}, "success": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 60, "name": "success", "strict": null, "type": "builtins.bool"}, "task_id": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 59, "name": "task_id", "strict": null, "type": "builtins.str"}, "worker_id": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 62, "name": "worker_id", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}}, "module_name": "src.data_trans.executor.ray_task_executor", "mro": ["src.data_trans.executor.ray_task_executor.RayTaskResult", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "task_id", "success", "execution_time", "worker_id", "crawled_count", "cleaned_count", "stored_count", "error_message", "error_details", "logs", "metrics"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "task_id", "success", "execution_time", "worker_id", "crawled_count", "cleaned_count", "stored_count", "error_message", "error_details", "logs", "metrics"], "arg_types": ["src.data_trans.executor.ray_task_executor.RayTaskResult", "builtins.str", "builtins.bool", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayTaskResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "cleaned_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.cleaned_count", "name": "cleaned_count", "setter_type": null, "type": "builtins.int"}}, "crawled_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.crawled_count", "name": "crawled_count", "setter_type": null, "type": "builtins.int"}}, "error_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.error_details", "name": "error_details", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "error_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.error_message", "name": "error_message", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "execution_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.execution_time", "name": "execution_time", "setter_type": null, "type": "builtins.float"}}, "logs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.logs", "name": "logs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.metrics", "name": "metrics", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "task_id", "success", "execution_time", "worker_id", "crawled_count", "cleaned_count", "stored_count", "error_message", "error_details", "logs", "metrics"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "task_id", "success", "execution_time", "worker_id", "crawled_count", "cleaned_count", "stored_count", "error_message", "error_details", "logs", "metrics"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_task_executor.RayTaskResult"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayTaskResult", "ret_type": "src.data_trans.executor.ray_task_executor.RayTaskResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "task_id", "success", "execution_time", "worker_id", "crawled_count", "cleaned_count", "stored_count", "error_message", "error_details", "logs", "metrics"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_task_executor.RayTaskResult"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayTaskResult", "ret_type": "src.data_trans.executor.ray_task_executor.RayTaskResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "stored_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.stored_count", "name": "stored_count", "setter_type": null, "type": "builtins.int"}}, "success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.success", "name": "success", "setter_type": null, "type": "builtins.bool"}}, "task_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.task_id", "name": "task_id", "setter_type": null, "type": "builtins.str"}}, "worker_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.worker_id", "name": "worker_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_task_executor.RayTaskResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_task_executor.RayTaskResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TaskConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskExecutor": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskExecutor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskExecutorConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskExecutorConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_task_executor.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "execute_ray_task": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["task_config_dict", "executor_config_dict", "settings_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "src.data_trans.executor.ray_task_executor.execute_ray_task", "name": "execute_ray_task", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["task_config_dict", "executor_config_dict", "settings_dict"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute_ray_task", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.ray_task_executor.RayTaskResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_task_executor.execute_ray_task", "name": "execute_ray_task", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_task_executor.remote", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_task_executor.remote", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get_ray_config_manager": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.ray_config.get_ray_config_manager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_settings": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.config.settings.get_settings", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_task_executor.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_task_executor.structlog", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_task_executor.structlog", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ray": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_task_executor.ray", "name": "ray", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_task_executor.ray", "source_any": null, "type_of_any": 3}}}, "remote": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_task_executor.remote", "name": "remote", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_task_executor.remote", "source_any": null, "type_of_any": 3}}}, "structlog": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_task_executor.structlog", "name": "structlog", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_task_executor.structlog", "source_any": null, "type_of_any": 3}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "D:\\github_projects\\data_trans\\src\\data_trans\\executor\\ray_task_executor.py"}