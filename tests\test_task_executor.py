"""
TaskExecutor测试模块

测试数据采集任务执行器的各项功能。
"""

import asyncio
import json
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.data_trans.executor import TaskExecutor, TaskExecutorConfig
from src.data_trans.executor.base_executor import ExecutionStatus
from src.data_trans.executor.task_executor import TaskConfig


class TestTaskExecutor:
    """TaskExecutor测试类"""

    @pytest.fixture
    def executor_config(self):
        """执行器配置fixture"""
        return TaskExecutorConfig(
            max_concurrent_tasks=2, timeout=30.0, enable_metrics=True, log_level="DEBUG"
        )

    @pytest.fixture
    def executor(self, executor_config):
        """执行器实例fixture"""
        return TaskExecutor(executor_config)

    @pytest.fixture
    def sample_task_config(self):
        """示例任务配置fixture"""
        return {
            "task_id": "test_task_001",
            "crawler_type": "web",
            "urls": ["https://httpbin.org/json"],
            "cleaner_type": "text",
            "storage_type": "mongodb",
            "raw_collection": "test_raw",
            "cleaned_collection": "test_cleaned",
        }

    @pytest.mark.asyncio
    async def test_task_config_validation(self):
        """测试任务配置验证"""
        # 有效配置
        valid_config = {"task_id": "test_001", "urls": ["https://example.com"]}
        config = TaskConfig(**valid_config)
        assert config.task_id == "test_001"
        assert config.urls == ["https://example.com"]
        assert config.crawler_type == "web"  # 默认值

        # 无效配置 - 缺少必需字段
        with pytest.raises(ValueError):
            TaskConfig(task_id="test_002")  # 缺少urls

    @pytest.mark.asyncio
    async def test_executor_initialization(self, executor):
        """测试执行器初始化"""
        assert executor.config.max_concurrent_tasks == 2
        assert executor.config.timeout == 30.0
        assert len(executor._running_tasks) == 0
        assert len(executor._storage_cache) == 0

    @pytest.mark.asyncio
    async def test_execution_context_manager(self, executor):
        """测试执行上下文管理器"""
        task_id = "test_context_001"

        async with executor.execution_context(task_id) as result:
            assert result.task_id == task_id
            assert result.status == ExecutionStatus.RUNNING
            assert result.start_time is not None
            assert task_id in executor._running_tasks

        # 上下文退出后状态应该更新
        assert result.status in [ExecutionStatus.SUCCESS, ExecutionStatus.FAILED]
        assert result.end_time is not None
        assert result.duration is not None

    @pytest.mark.asyncio
    async def test_execution_context_with_exception(self, executor):
        """测试执行上下文异常处理"""
        task_id = "test_exception_001"

        try:
            async with executor.execution_context(task_id) as result:
                raise ValueError("测试异常")
        except ValueError:
            pass

        # 异常后状态应该是失败
        assert result.status == ExecutionStatus.FAILED
        assert "测试异常" in result.error_message

    @pytest.mark.asyncio
    async def test_execution_context_timeout(self, executor):
        """测试执行上下文超时处理"""
        task_id = "test_timeout_001"

        try:
            async with executor.execution_context(task_id) as result:
                raise asyncio.TimeoutError()
        except asyncio.TimeoutError:
            pass

        assert result.status == ExecutionStatus.TIMEOUT
        assert "超时" in result.error_message

    @pytest.mark.asyncio
    @patch("src.data_trans.executor.task_executor.WebCrawler")
    @patch("src.data_trans.executor.task_executor.MongoDBStorage")
    async def test_execute_crawl_success(
        self, mock_storage, mock_crawler, executor, sample_task_config
    ):
        """测试爬取执行成功"""
        # Mock爬虫
        mock_crawler_instance = AsyncMock()
        mock_crawler.return_value = mock_crawler_instance

        # Mock爬取结果
        mock_crawl_result = MagicMock()
        mock_crawl_result.success = True
        mock_crawl_result.data = {"test": "data"}
        mock_crawl_result.metadata = {"url": "https://httpbin.org/json"}
        mock_crawl_result.timestamp = datetime.utcnow()

        mock_crawler_instance.fetch_single.return_value = mock_crawl_result

        # Mock存储
        mock_storage_instance = AsyncMock()
        mock_storage.return_value = mock_storage_instance
        mock_storage_instance.is_connected = True

        # 执行任务
        result = await executor.execute_task("test_001", sample_task_config)

        # 验证结果
        assert result.task_id == "test_001"
        assert result.crawled_count > 0

        # 验证爬虫被调用
        mock_crawler_instance.setup.assert_called_once()
        mock_crawler_instance.fetch_single.assert_called_once()
        mock_crawler_instance.cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_batch_execution(self, executor):
        """测试批量执行"""
        tasks = [
            {"task_id": "batch_001", "urls": ["https://example1.com"]},
            {"task_id": "batch_002", "urls": ["https://example2.com"]},
        ]

        with patch.object(executor, "execute_task") as mock_execute:
            # Mock单个任务执行
            mock_execute.side_effect = [
                MagicMock(task_id="batch_001", status=ExecutionStatus.SUCCESS),
                MagicMock(task_id="batch_002", status=ExecutionStatus.SUCCESS),
            ]

            results = await executor.execute_batch(tasks)

            assert len(results) == 2
            assert mock_execute.call_count == 2

    @pytest.mark.asyncio
    async def test_task_status_management(self, executor):
        """测试任务状态管理"""
        task_id = "status_test_001"

        # 初始状态
        assert executor.get_task_status(task_id) is None

        # 执行任务时状态管理
        async with executor.execution_context(task_id) as result:
            # 运行中状态
            assert executor.get_task_status(task_id) == result
            assert result.status == ExecutionStatus.RUNNING

        # 完成后状态
        final_result = executor.get_task_status(task_id)
        assert final_result is not None
        assert final_result.status in [ExecutionStatus.SUCCESS, ExecutionStatus.FAILED]

    @pytest.mark.asyncio
    async def test_task_cancellation(self, executor):
        """测试任务取消"""
        task_id = "cancel_test_001"

        async with executor.execution_context(task_id) as result:
            # 取消运行中的任务
            success = executor.cancel_task(task_id)
            assert success is True
            assert result.status == ExecutionStatus.CANCELLED

        # 取消不存在的任务
        success = executor.cancel_task("nonexistent")
        assert success is False

    @pytest.mark.asyncio
    async def test_execution_stats(self, executor):
        """测试执行统计"""
        # 初始统计
        stats = executor.get_execution_stats()
        assert stats["total_tasks"] == 0
        assert stats["success_rate"] == 0.0

        # 添加一些任务结果
        executor._running_tasks["task1"] = MagicMock(
            status=ExecutionStatus.SUCCESS,
            crawled_count=10,
            cleaned_count=8,
            stored_count=8,
            duration=5.0,
        )
        executor._running_tasks["task2"] = MagicMock(
            status=ExecutionStatus.FAILED,
            crawled_count=5,
            cleaned_count=0,
            stored_count=0,
            duration=2.0,
        )

        stats = executor.get_execution_stats()
        assert stats["total_tasks"] == 2
        assert stats["total_crawled"] == 15
        assert stats["total_cleaned"] == 8
        assert stats["total_stored"] == 8
        assert stats["success_rate"] == 0.5
        assert stats["average_duration"] == 3.5

    @pytest.mark.asyncio
    async def test_config_file_loading(self, executor, tmp_path):
        """测试配置文件加载"""
        # 创建测试配置文件
        config_data = {
            "task_id": "file_test_001",
            "urls": ["https://example.com"],
            "crawler_type": "web",
        }

        config_file = tmp_path / "test_config.json"
        with open(config_file, "w") as f:
            json.dump(config_data, f)

        # 加载配置
        loaded_config = await executor.load_task_from_file(config_file)
        assert loaded_config.task_id == "file_test_001"
        assert loaded_config.urls == ["https://example.com"]
        assert loaded_config.crawler_type == "web"

        # 测试不存在的文件
        with pytest.raises(FileNotFoundError):
            await executor.load_task_from_file("nonexistent.json")

    @pytest.mark.asyncio
    async def test_cleanup(self, executor):
        """测试资源清理"""
        # 添加一些运行中的任务
        executor._running_tasks["task1"] = MagicMock(status=ExecutionStatus.RUNNING)
        executor._running_tasks["task2"] = MagicMock(status=ExecutionStatus.SUCCESS)

        # 添加存储缓存
        mock_storage = AsyncMock()
        executor._storage_cache["test_storage"] = mock_storage

        await executor.cleanup()

        # 验证运行中的任务被取消
        assert executor._running_tasks["task1"].status == ExecutionStatus.CANCELLED

        # 验证存储连接被关闭
        mock_storage.disconnect.assert_called_once()

        # 验证缓存被清空
        assert len(executor._storage_cache) == 0
