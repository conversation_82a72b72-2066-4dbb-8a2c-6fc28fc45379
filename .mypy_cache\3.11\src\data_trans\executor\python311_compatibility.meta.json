{"data_mtime": 1752391814, "dep_lines": [7, 8, 9, 10, 11, 12, 13, 16, 117, 138, 170, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 15, 371], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20], "dependencies": ["asyncio", "gc", "logging", "sys", "time", "datetime", "typing", "pydantic", "builtins", "typing_extensions", "<PERSON><PERSON><PERSON><PERSON>", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.types", "re", "types"], "hash": "4707299cbc954294a37098a37ada2f21a535ffd5", "id": "src.data_trans.executor.python311_compatibility", "ignore_all": false, "interface_hash": "24e851bdf61981577ac24cce6b8edf288b9f48a2", "mtime": 1752391023, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "D:\\github_projects\\data_trans\\src\\data_trans\\executor\\python311_compatibility.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 17610, "suppressed": ["structlog", "psutil"], "version_id": "1.16.1"}