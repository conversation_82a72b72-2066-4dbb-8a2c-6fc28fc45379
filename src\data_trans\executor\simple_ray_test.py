"""
简单的Ray功能测试

测试Ray的基本功能和Python 3.11兼容性。
"""

import asyncio
import logging
import time
from typing import Any, Dict

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Ray导入测试
try:
    import ray

    RAY_AVAILABLE = True
    logger.info(f"Ray successfully imported, version: {ray.__version__}")
except ImportError as e:
    RAY_AVAILABLE = False
    logger.warning(f"Ray not available: {e}")


def test_ray_import():
    """测试Ray导入"""
    logger.info("测试Ray导入...")

    if RAY_AVAILABLE:
        logger.info("✓ Ray导入成功")
        logger.info(f"Ray版本: {ray.__version__}")
        return True
    else:
        logger.warning("✗ Ray导入失败")
        return False


def test_ray_initialization():
    """测试Ray初始化"""
    logger.info("测试Ray初始化...")

    if not RAY_AVAILABLE:
        logger.warning("Ray不可用，跳过初始化测试")
        return False

    try:
        # 检查是否已经初始化
        if ray.is_initialized():
            logger.info("Ray已经初始化")
            return True

        # 初始化Ray（本地模式）
        ray.init(local_mode=True, ignore_reinit_error=True, log_to_driver=False)

        if ray.is_initialized():
            logger.info("✓ Ray初始化成功")

            # 获取集群信息
            cluster_resources = ray.cluster_resources()
            logger.info(f"集群资源: {cluster_resources}")

            return True
        else:
            logger.error("✗ Ray初始化失败")
            return False

    except Exception as e:
        logger.error(f"✗ Ray初始化异常: {e}")
        return False


@ray.remote
def simple_ray_task(task_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """简单的Ray远程任务"""
    start_time = time.time()

    # 模拟一些工作
    time.sleep(1)

    execution_time = time.time() - start_time

    return {
        "task_id": task_id,
        "input_data": data,
        "execution_time": execution_time,
        "worker_id": ray.get_runtime_context().get_worker_id(),
        "success": True,
    }


def test_ray_remote_function():
    """测试Ray远程函数"""
    logger.info("测试Ray远程函数...")

    if not RAY_AVAILABLE or not ray.is_initialized():
        logger.warning("Ray不可用或未初始化，跳过远程函数测试")
        return False

    try:
        # 提交任务
        task_data = {"test": True, "value": 42}
        future = simple_ray_task.remote("test_task_001", task_data)

        # 等待结果
        result = ray.get(future)

        logger.info(f"✓ Ray远程函数执行成功")
        logger.info(f"任务ID: {result['task_id']}")
        logger.info(f"执行时间: {result['execution_time']:.2f}秒")
        logger.info(f"工作节点ID: {result['worker_id']}")

        return True

    except Exception as e:
        logger.error(f"✗ Ray远程函数测试失败: {e}")
        return False


def test_ray_batch_execution():
    """测试Ray批量执行"""
    logger.info("测试Ray批量执行...")

    if not RAY_AVAILABLE or not ray.is_initialized():
        logger.warning("Ray不可用或未初始化，跳过批量执行测试")
        return False

    try:
        # 提交多个任务
        futures = []
        for i in range(3):
            task_data = {"batch_id": i, "test": True}
            future = simple_ray_task.remote(f"batch_task_{i}", task_data)
            futures.append(future)

        # 等待所有任务完成
        results = ray.get(futures)

        logger.info(f"✓ Ray批量执行成功，完成 {len(results)} 个任务")

        for result in results:
            logger.info(
                f"任务 {result['task_id']} 完成，耗时: {result['execution_time']:.2f}秒"
            )

        return True

    except Exception as e:
        logger.error(f"✗ Ray批量执行测试失败: {e}")
        return False


@ray.remote
class SimpleRayActor:
    """简单的Ray Actor"""

    def __init__(self, actor_id: str):
        self.actor_id = actor_id
        self.counter = 0
        self.start_time = time.time()

    def increment(self) -> int:
        """增加计数器"""
        self.counter += 1
        return self.counter

    def get_status(self) -> Dict[str, Any]:
        """获取状态"""
        return {
            "actor_id": self.actor_id,
            "counter": self.counter,
            "uptime": time.time() - self.start_time,
            "worker_id": ray.get_runtime_context().get_worker_id(),
        }


def test_ray_actor():
    """测试Ray Actor"""
    logger.info("测试Ray Actor...")

    if not RAY_AVAILABLE or not ray.is_initialized():
        logger.warning("Ray不可用或未初始化，跳过Actor测试")
        return False

    try:
        # 创建Actor
        actor = SimpleRayActor.remote("test_actor")

        # 调用Actor方法
        count1 = ray.get(actor.increment.remote())
        count2 = ray.get(actor.increment.remote())
        count3 = ray.get(actor.increment.remote())

        # 获取状态
        status = ray.get(actor.get_status.remote())

        logger.info(f"✓ Ray Actor测试成功")
        logger.info(f"计数器值: {count1}, {count2}, {count3}")
        logger.info(f"Actor状态: {status}")

        return True

    except Exception as e:
        logger.error(f"✗ Ray Actor测试失败: {e}")
        return False


def test_ray_cleanup():
    """测试Ray清理"""
    logger.info("测试Ray清理...")

    if not RAY_AVAILABLE:
        logger.warning("Ray不可用，跳过清理测试")
        return True

    try:
        if ray.is_initialized():
            ray.shutdown()
            logger.info("✓ Ray清理成功")
        else:
            logger.info("Ray未初始化，无需清理")

        return True

    except Exception as e:
        logger.error(f"✗ Ray清理失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行Ray基础功能测试")

    tests = [
        ("Ray导入", test_ray_import),
        ("Ray初始化", test_ray_initialization),
        ("Ray远程函数", test_ray_remote_function),
        ("Ray批量执行", test_ray_batch_execution),
        ("Ray Actor", test_ray_actor),
        ("Ray清理", test_ray_cleanup),
    ]

    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n--- 运行测试: {test_name} ---")
        try:
            result = test_func()
            results[test_name] = result
            status = "通过" if result else "失败"
            logger.info(f"测试结果: {test_name} - {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"测试异常: {test_name} - {e}")

    # 汇总结果
    logger.info("\n" + "=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)

    total_tests = len(tests)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests

    for test_name, result in results.items():
        status = "✓" if result else "✗"
        logger.info(f"{status} {test_name}")

    logger.info(f"\n总计: {total_tests} 个测试")
    logger.info(f"通过: {passed_tests} 个")
    logger.info(f"失败: {failed_tests} 个")
    logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%")

    return passed_tests == total_tests


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
