"""
Ray分布式状态管理器

使用Ray Actor实现分布式状态管理，支持状态同步、持久化和故障恢复。
充分利用Python 3.11的性能优化和新特性。
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import structlog
from pydantic import BaseModel, Field

# Ray导入和错误处理
try:
    import ray
    from ray import actor

    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False

    def actor(*args, **kwargs):
        def decorator(cls):
            cls._is_ray_actor = True
            return cls

        return decorator


logger = structlog.get_logger(__name__)


class StateEntry(BaseModel):
    """状态条目"""

    key: str = Field(description="状态键")
    value: Any = Field(description="状态值")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")
    ttl: Optional[int] = Field(default=None, description="生存时间(秒)")
    version: int = Field(default=1, description="版本号")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class StateSnapshot(BaseModel):
    """状态快照"""

    snapshot_id: str = Field(description="快照ID")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="快照时间")
    entries: Dict[str, StateEntry] = Field(default_factory=dict, description="状态条目")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="快照元数据")


@actor
class DistributedStateActor:
    """分布式状态Actor

    管理分布式状态的存储、同步和持久化。
    """

    def __init__(
        self, actor_id: str = "state_manager", persist_path: Optional[str] = None
    ):
        """初始化状态Actor

        Args:
            actor_id: Actor ID
            persist_path: 持久化路径
        """
        self.actor_id = actor_id
        self.persist_path = persist_path
        self.logger = structlog.get_logger(__name__).bind(actor_id=actor_id)

        # 状态存储
        self.state: Dict[str, StateEntry] = {}
        self.state_lock = asyncio.Lock()

        # 版本控制
        self.global_version = 0

        # 快照管理
        self.snapshots: Dict[str, StateSnapshot] = {}
        self.max_snapshots = 10

        # 同步状态
        self.sync_peers: List[str] = []  # 其他状态Actor的引用
        self.last_sync_time: Optional[datetime] = None

        # 持久化状态
        self.auto_persist = True
        self.persist_interval = 300  # 5分钟
        self.last_persist_time: Optional[datetime] = None

        # 清理状态
        self.cleanup_interval = 60  # 1分钟
        self.last_cleanup_time: Optional[datetime] = None

        self.logger.info("DistributedStateActor初始化完成")

        # 加载持久化状态
        if self.persist_path:
            asyncio.create_task(self._load_persisted_state())

        # 启动后台任务
        if RAY_AVAILABLE:
            asyncio.create_task(self._background_tasks())

    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """设置状态值

        Args:
            key: 状态键
            value: 状态值
            ttl: 生存时间(秒)
            metadata: 元数据

        Returns:
            是否设置成功
        """
        try:
            async with self.state_lock:
                # 检查是否存在，如果存在则增加版本号
                version = 1
                if key in self.state:
                    version = self.state[key].version + 1

                # 创建状态条目
                entry = StateEntry(
                    key=key,
                    value=value,
                    ttl=ttl,
                    version=version,
                    metadata=metadata or {},
                )

                self.state[key] = entry
                self.global_version += 1

                self.logger.debug("状态设置成功", key=key, version=version)

                # 异步同步到其他节点
                if self.sync_peers:
                    asyncio.create_task(self._sync_to_peers(key, entry))

                return True

        except Exception as e:
            self.logger.error("状态设置失败", key=key, error=str(e))
            return False

    async def get(self, key: str) -> Optional[Any]:
        """获取状态值

        Args:
            key: 状态键

        Returns:
            状态值，如果不存在或已过期则返回None
        """
        try:
            async with self.state_lock:
                if key not in self.state:
                    return None

                entry = self.state[key]

                # 检查TTL
                if entry.ttl is not None:
                    elapsed = (datetime.utcnow() - entry.timestamp).total_seconds()
                    if elapsed > entry.ttl:
                        # 已过期，删除
                        del self.state[key]
                        self.logger.debug("状态已过期并删除", key=key)
                        return None

                return entry.value

        except Exception as e:
            self.logger.error("状态获取失败", key=key, error=str(e))
            return None

    async def delete(self, key: str) -> bool:
        """删除状态

        Args:
            key: 状态键

        Returns:
            是否删除成功
        """
        try:
            async with self.state_lock:
                if key in self.state:
                    del self.state[key]
                    self.global_version += 1
                    self.logger.debug("状态删除成功", key=key)

                    # 同步删除到其他节点
                    if self.sync_peers:
                        asyncio.create_task(self._sync_delete_to_peers(key))

                    return True
                else:
                    return False

        except Exception as e:
            self.logger.error("状态删除失败", key=key, error=str(e))
            return False

    async def exists(self, key: str) -> bool:
        """检查状态是否存在

        Args:
            key: 状态键

        Returns:
            是否存在
        """
        value = await self.get(key)
        return value is not None

    async def keys(self, pattern: Optional[str] = None) -> List[str]:
        """获取所有键

        Args:
            pattern: 键模式（简单的前缀匹配）

        Returns:
            键列表
        """
        try:
            async with self.state_lock:
                # 清理过期状态
                await self._cleanup_expired_states()

                keys = list(self.state.keys())

                if pattern:
                    keys = [key for key in keys if key.startswith(pattern)]

                return keys

        except Exception as e:
            self.logger.error("获取键列表失败", error=str(e))
            return []

    async def size(self) -> int:
        """获取状态数量

        Returns:
            状态数量
        """
        try:
            async with self.state_lock:
                await self._cleanup_expired_states()
                return len(self.state)
        except Exception as e:
            self.logger.error("获取状态数量失败", error=str(e))
            return 0

    async def clear(self) -> bool:
        """清空所有状态

        Returns:
            是否清空成功
        """
        try:
            async with self.state_lock:
                self.state.clear()
                self.global_version += 1
                self.logger.info("状态已清空")
                return True
        except Exception as e:
            self.logger.error("清空状态失败", error=str(e))
            return False

    async def create_snapshot(self, snapshot_id: Optional[str] = None) -> str:
        """创建状态快照

        Args:
            snapshot_id: 快照ID，如果不提供则自动生成

        Returns:
            快照ID
        """
        try:
            if not snapshot_id:
                snapshot_id = f"snapshot_{int(time.time())}"

            async with self.state_lock:
                # 清理过期状态
                await self._cleanup_expired_states()

                # 创建快照
                snapshot = StateSnapshot(
                    snapshot_id=snapshot_id,
                    entries=self.state.copy(),
                    metadata={
                        "global_version": self.global_version,
                        "entry_count": len(self.state),
                    },
                )

                self.snapshots[snapshot_id] = snapshot

                # 限制快照数量
                if len(self.snapshots) > self.max_snapshots:
                    oldest_id = min(
                        self.snapshots.keys(), key=lambda x: self.snapshots[x].timestamp
                    )
                    del self.snapshots[oldest_id]

                self.logger.info("快照创建成功", snapshot_id=snapshot_id)
                return snapshot_id

        except Exception as e:
            self.logger.error("创建快照失败", error=str(e))
            return ""

    async def restore_snapshot(self, snapshot_id: str) -> bool:
        """恢复状态快照

        Args:
            snapshot_id: 快照ID

        Returns:
            是否恢复成功
        """
        try:
            if snapshot_id not in self.snapshots:
                self.logger.error("快照不存在", snapshot_id=snapshot_id)
                return False

            async with self.state_lock:
                snapshot = self.snapshots[snapshot_id]
                self.state = snapshot.entries.copy()
                self.global_version = snapshot.metadata.get("global_version", 0)

                self.logger.info(
                    "快照恢复成功", snapshot_id=snapshot_id, entry_count=len(self.state)
                )
                return True

        except Exception as e:
            self.logger.error("恢复快照失败", snapshot_id=snapshot_id, error=str(e))
            return False

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息

        Returns:
            统计信息字典
        """
        try:
            async with self.state_lock:
                await self._cleanup_expired_states()

                # 计算TTL分布
                ttl_stats = {"no_ttl": 0, "with_ttl": 0}
                for entry in self.state.values():
                    if entry.ttl is None:
                        ttl_stats["no_ttl"] += 1
                    else:
                        ttl_stats["with_ttl"] += 1

                return {
                    "actor_id": self.actor_id,
                    "total_entries": len(self.state),
                    "global_version": self.global_version,
                    "snapshot_count": len(self.snapshots),
                    "sync_peers": len(self.sync_peers),
                    "ttl_distribution": ttl_stats,
                    "last_sync_time": (
                        self.last_sync_time.isoformat() if self.last_sync_time else None
                    ),
                    "last_persist_time": (
                        self.last_persist_time.isoformat()
                        if self.last_persist_time
                        else None
                    ),
                    "last_cleanup_time": (
                        self.last_cleanup_time.isoformat()
                        if self.last_cleanup_time
                        else None
                    ),
                }

        except Exception as e:
            self.logger.error("获取统计信息失败", error=str(e))
            return {"error": str(e)}

    async def _cleanup_expired_states(self) -> int:
        """清理过期状态

        Returns:
            清理的状态数量
        """
        try:
            now = datetime.utcnow()
            expired_keys = []

            for key, entry in self.state.items():
                if entry.ttl is not None:
                    elapsed = (now - entry.timestamp).total_seconds()
                    if elapsed > entry.ttl:
                        expired_keys.append(key)

            for key in expired_keys:
                del self.state[key]

            if expired_keys:
                self.global_version += 1
                self.logger.debug("清理过期状态", count=len(expired_keys))

            self.last_cleanup_time = now
            return len(expired_keys)

        except Exception as e:
            self.logger.error("清理过期状态失败", error=str(e))
            return 0

    async def _sync_to_peers(self, key: str, entry: StateEntry) -> None:
        """同步状态到其他节点"""
        # 这里需要实现与其他StateActor的同步逻辑
        # 由于复杂性，这里只记录日志
        self.logger.debug("同步状态到其他节点", key=key, peers=len(self.sync_peers))

    async def _sync_delete_to_peers(self, key: str) -> None:
        """同步删除到其他节点"""
        self.logger.debug("同步删除到其他节点", key=key, peers=len(self.sync_peers))

    async def _background_tasks(self) -> None:
        """后台任务"""
        while True:
            try:
                now = datetime.utcnow()

                # 定期清理过期状态
                if (
                    not self.last_cleanup_time
                    or (now - self.last_cleanup_time).total_seconds()
                    > self.cleanup_interval
                ):
                    await self._cleanup_expired_states()

                # 定期持久化
                if (
                    self.auto_persist
                    and self.persist_path
                    and (
                        not self.last_persist_time
                        or (now - self.last_persist_time).total_seconds()
                        > self.persist_interval
                    )
                ):
                    await self._persist_state()

                await asyncio.sleep(30)  # 30秒检查一次

            except Exception as e:
                self.logger.error("后台任务异常", error=str(e))
                await asyncio.sleep(30)

    async def _persist_state(self) -> bool:
        """持久化状态"""
        if not self.persist_path:
            return False

        try:
            # 创建快照用于持久化
            snapshot_id = await self.create_snapshot("persist_snapshot")
            snapshot = self.snapshots[snapshot_id]

            # 序列化并保存
            with open(self.persist_path, "w", encoding="utf-8") as f:
                json.dump(
                    snapshot.model_dump(), f, ensure_ascii=False, indent=2, default=str
                )

            self.last_persist_time = datetime.utcnow()
            self.logger.info("状态持久化成功", path=self.persist_path)
            return True

        except Exception as e:
            self.logger.error("状态持久化失败", error=str(e))
            return False

    async def _load_persisted_state(self) -> bool:
        """加载持久化状态"""
        if not self.persist_path:
            return False

        try:
            import os

            if not os.path.exists(self.persist_path):
                self.logger.info("持久化文件不存在，跳过加载", path=self.persist_path)
                return False

            # 加载并反序列化
            with open(self.persist_path, "r", encoding="utf-8") as f:
                snapshot_data = json.load(f)

            # 重建 StateSnapshot 对象
            async with self.state_lock:
                # 重建状态条目
                entries = {}
                for key, entry_data in snapshot_data.get("entries", {}).items():
                    entries[key] = StateEntry(**entry_data)

                # 重建快照
                snapshot = StateSnapshot(
                    snapshot_id=snapshot_data["snapshot_id"],
                    timestamp=datetime.fromisoformat(
                        snapshot_data["timestamp"].replace("Z", "+00:00")
                    ),
                    entries=entries,
                    metadata=snapshot_data.get("metadata", {}),
                )

                # 恢复状态
                self.state = entries.copy()
                self.global_version = snapshot.metadata.get("global_version", 0)

                # 保存快照
                self.snapshots[snapshot.snapshot_id] = snapshot

                self.logger.info(
                    "持久化状态加载成功",
                    path=self.persist_path,
                    entry_count=len(self.state),
                    global_version=self.global_version,
                )
                return True

        except Exception as e:
            self.logger.error("加载持久化状态失败", error=str(e))
            return False


class RayStateManager:
    """Ray状态管理器客户端

    提供对分布式状态的高级访问接口。
    """

    def __init__(self, actor_id: str = "state_manager"):
        """初始化状态管理器客户端

        Args:
            actor_id: 状态Actor ID
        """
        self.actor_id = actor_id
        self.state_actor: Optional[Any] = None

        if RAY_AVAILABLE:
            self._initialize_actor()

    def _initialize_actor(self) -> None:
        """初始化状态Actor"""
        try:
            if ray.is_initialized():
                self.state_actor = DistributedStateActor.remote(self.actor_id)
                logger.info("状态Actor初始化成功", actor_id=self.actor_id)
            else:
                logger.warning("Ray未初始化，状态管理器不可用")
        except Exception as e:
            logger.error("状态Actor初始化失败", error=str(e))

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置状态值"""
        if not self.state_actor:
            return False

        try:
            return await self.state_actor.set.remote(key, value, ttl)
        except Exception as e:
            logger.error("设置状态失败", key=key, error=str(e))
            return False

    async def get(self, key: str) -> Optional[Any]:
        """获取状态值"""
        if not self.state_actor:
            return None

        try:
            return await self.state_actor.get.remote(key)
        except Exception as e:
            logger.error("获取状态失败", key=key, error=str(e))
            return None

    async def delete(self, key: str) -> bool:
        """删除状态"""
        if not self.state_actor:
            return False

        try:
            return await self.state_actor.delete.remote(key)
        except Exception as e:
            logger.error("删除状态失败", key=key, error=str(e))
            return False

    async def exists(self, key: str) -> bool:
        """检查状态是否存在"""
        if not self.state_actor:
            return False

        try:
            return await self.state_actor.exists.remote(key)
        except Exception as e:
            logger.error("检查状态存在失败", key=key, error=str(e))
            return False

    async def keys(self, pattern: Optional[str] = None) -> List[str]:
        """获取所有键"""
        if not self.state_actor:
            return []

        try:
            return await self.state_actor.keys.remote(pattern)
        except Exception as e:
            logger.error("获取键列表失败", error=str(e))
            return []

    async def create_snapshot(self, snapshot_id: Optional[str] = None) -> str:
        """创建状态快照"""
        if not self.state_actor:
            return ""

        try:
            return await self.state_actor.create_snapshot.remote(snapshot_id)
        except Exception as e:
            logger.error("创建快照失败", error=str(e))
            return ""

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.state_actor:
            return {"error": "状态Actor不可用"}

        try:
            return await self.state_actor.get_statistics.remote()
        except Exception as e:
            logger.error("获取统计信息失败", error=str(e))
            return {"error": str(e)}
