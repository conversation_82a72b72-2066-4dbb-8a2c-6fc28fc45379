{".class": "MypyFile", "_fullname": "src.data_trans.executor.cli", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskExecutor": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskExecutor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskExecutorConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskExecutorConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.cli.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.cli.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.cli.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.cli.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.cli.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.cli.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "batch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["ctx", "config_dir", "pattern", "max_concurrent", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "src.data_trans.executor.cli.batch", "name": "batch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["ctx", "config_dir", "pattern", "max_concurrent", "timeout"], "arg_types": [{".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": null, "type_of_any": 3}, "builtins.str", "builtins.str", "builtins.int", "builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "src.data_trans.executor.cli.batch", "name": "batch", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ctx", "log_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "src.data_trans.executor.cli.cli", "name": "cli", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ctx", "log_level"], "arg_types": [{".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": null, "type_of_any": 3}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cli", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "src.data_trans.executor.cli.cli", "name": "cli", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "click": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.cli.click", "name": "click", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.cli.main", "name": "main", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "main", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["ctx", "config", "timeout", "max_concurrent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "src.data_trans.executor.cli.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["ctx", "config", "timeout", "max_concurrent"], "arg_types": [{".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": null, "type_of_any": 3}, "builtins.str", "builtins.float", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "src.data_trans.executor.cli.run", "name": "run", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "setup_logging": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.cli.setup_logging", "name": "setup_logging", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["level"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup_logging", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "structlog": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.cli.structlog", "name": "structlog", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.structlog", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["output", "crawler_type", "storage_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "src.data_trans.executor.cli.template", "name": "template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["output", "crawler_type", "storage_type"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "template", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "src.data_trans.executor.cli.template", "name": "template", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.cli.click", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "path": "src\\data_trans\\executor\\cli.py"}