{".class": "MypyFile", "_fullname": "tests.test_task_executor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.AsyncMock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExecutionStatus": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.base_executor.ExecutionStatus", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MagicMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.MagicMock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskExecutor": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskExecutor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskExecutorConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskExecutorConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TestTaskExecutor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_task_executor.TestTaskExecutor", "name": "TestTaskExecutor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_task_executor.TestTaskExecutor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_task_executor", "mro": ["tests.test_task_executor.TestTaskExecutor", "builtins.object"], "names": {".class": "SymbolTable", "executor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.executor", "name": "executor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.executor", "name": "executor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "executor_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.executor_config", "name": "executor_config", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.executor_config", "name": "executor_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "sample_task_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.sample_task_config", "name": "sample_task_config", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.sample_task_config", "name": "sample_task_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_batch_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_batch_execution", "name": "test_batch_execution", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_batch_execution", "name": "test_batch_execution", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_cleanup", "name": "test_cleanup", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_cleanup", "name": "test_cleanup", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_config_file_loading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "executor", "tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_config_file_loading", "name": "test_config_file_loading", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_config_file_loading", "name": "test_config_file_loading", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_execute_crawl_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "mock_storage", "mock_crawler", "executor", "sample_task_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execute_crawl_success", "name": "test_execute_crawl_success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execute_crawl_success", "name": "test_execute_crawl_success", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_execution_context_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execution_context_manager", "name": "test_execution_context_manager", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execution_context_manager", "name": "test_execution_context_manager", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_execution_context_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execution_context_timeout", "name": "test_execution_context_timeout", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execution_context_timeout", "name": "test_execution_context_timeout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_execution_context_with_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execution_context_with_exception", "name": "test_execution_context_with_exception", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execution_context_with_exception", "name": "test_execution_context_with_exception", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_execution_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execution_stats", "name": "test_execution_stats", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_execution_stats", "name": "test_execution_stats", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_executor_initialization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_executor_initialization", "name": "test_executor_initialization", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_executor_initialization", "name": "test_executor_initialization", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_task_cancellation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_task_cancellation", "name": "test_task_cancellation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_task_cancellation", "name": "test_task_cancellation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_task_config_validation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_task_config_validation", "name": "test_task_config_validation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_task_config_validation", "name": "test_task_config_validation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_task_status_management": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_task_status_management", "name": "test_task_status_management", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.TestTaskExecutor.test_task_status_management", "name": "test_task_status_management", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_task_executor.TestTaskExecutor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_task_executor.TestTaskExecutor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_task_executor.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_task_executor.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_task_executor.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_task_executor.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_task_executor.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_task_executor.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_hidden": true, "module_public": false}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pytest": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_task_executor.pytest", "name": "pytest", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_task_executor.pytest", "source_any": null, "type_of_any": 3}}}}, "path": "tests\\test_task_executor.py"}