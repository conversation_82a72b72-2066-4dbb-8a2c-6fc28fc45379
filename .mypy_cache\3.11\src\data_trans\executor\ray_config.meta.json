{"data_mtime": 1752391814, "dep_lines": [13, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.data_trans.config.settings", "os", "typing", "pydantic", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "functools", "pydantic._internal", "pydantic._internal._decorators_v1", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.deprecated", "pydantic.deprecated.class_validators", "pydantic.fields", "pydantic.main", "pydantic.types", "re", "types", "typing_extensions"], "hash": "d9a1603469d413c71e5e4b5b4eed383a6e63d950", "id": "src.data_trans.executor.ray_config", "ignore_all": false, "interface_hash": "b9e9fa8776172df8c39bb323e679c00126487d06", "mtime": 1752389741, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "D:\\github_projects\\data_trans\\src\\data_trans\\executor\\ray_config.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 11476, "suppressed": [], "version_id": "1.16.1"}