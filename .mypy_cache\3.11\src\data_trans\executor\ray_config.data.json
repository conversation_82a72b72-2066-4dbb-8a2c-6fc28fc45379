{".class": "MypyFile", "_fullname": "src.data_trans.executor.ray_config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RayActorResourceConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig", "name": "RayActorResourceConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"lifetime": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 106, "name": "lifetime", "strict": null, "type": "builtins.str"}, "max_restarts": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 102, "name": "max_restarts", "strict": null, "type": "builtins.int"}, "max_task_retries": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 103, "name": "max_task_retries", "strict": null, "type": "builtins.int"}, "memory": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 93, "name": "memory", "strict": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "name": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 107, "name": "name", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "namespace": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 108, "name": "namespace", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "num_cpus": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 90, "name": "num_cpus", "strict": null, "type": "builtins.float"}, "num_gpus": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 96, "name": "num_gpus", "strict": null, "type": "builtins.float"}, "resources": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 99, "name": "resources", "strict": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}}, "module_name": "src.data_trans.executor.ray_config", "mro": ["src.data_trans.executor.ray_config.RayActorResourceConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "num_cpus", "memory", "num_gpus", "resources", "max_restarts", "max_task_retries", "lifetime", "name", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "num_cpus", "memory", "num_gpus", "resources", "max_restarts", "max_task_retries", "lifetime", "name", "namespace"], "arg_types": ["src.data_trans.executor.ray_config.RayActorResourceConfig", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayActorResourceConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "lifetime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.lifetime", "name": "lifetime", "setter_type": null, "type": "builtins.str"}}, "max_restarts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.max_restarts", "name": "max_restarts", "setter_type": null, "type": "builtins.int"}}, "max_task_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.max_task_retries", "name": "max_task_retries", "setter_type": null, "type": "builtins.int"}}, "memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.memory", "name": "memory", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "num_cpus", "memory", "num_gpus", "resources", "max_restarts", "max_task_retries", "lifetime", "name", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "num_cpus", "memory", "num_gpus", "resources", "max_restarts", "max_task_retries", "lifetime", "name", "namespace"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_config.RayActorResourceConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayActorResourceConfig", "ret_type": "src.data_trans.executor.ray_config.RayActorResourceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "num_cpus", "memory", "num_gpus", "resources", "max_restarts", "max_task_retries", "lifetime", "name", "namespace"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_config.RayActorResourceConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayActorResourceConfig", "ret_type": "src.data_trans.executor.ray_config.RayActorResourceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.name", "name": "name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.namespace", "name": "namespace", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "num_cpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.num_cpus", "name": "num_cpus", "setter_type": null, "type": "builtins.float"}}, "num_gpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.num_gpus", "name": "num_gpus", "setter_type": null, "type": "builtins.float"}}, "resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.resources", "name": "resources", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_config.RayActorResourceConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_config.RayActorResourceConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RayClusterConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_config.RayClusterConfig", "name": "RayClusterConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"address": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 20, "name": "address", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "dashboard_host": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 40, "name": "dashboard_host", "strict": null, "type": "builtins.str"}, "dashboard_port": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 41, "name": "dashboard_port", "strict": null, "type": "builtins.int"}, "ignore_reinit_error": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 38, "name": "ignore_reinit_error", "strict": null, "type": "builtins.bool"}, "include_dashboard": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 39, "name": "include_dashboard", "strict": null, "type": "builtins.bool"}, "local_mode": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 22, "name": "local_mode", "strict": null, "type": "builtins.bool"}, "log_to_driver": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 34, "name": "log_to_driver", "strict": null, "type": "builtins.bool"}, "logging_level": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 35, "name": "logging_level", "strict": null, "type": "builtins.str"}, "memory": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 30, "name": "memory", "strict": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "namespace": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 21, "name": "namespace", "strict": null, "type": "builtins.str"}, "num_cpus": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 28, "name": "num_cpus", "strict": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "num_gpus": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 29, "name": "num_gpus", "strict": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "object_store_memory": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 31, "name": "object_store_memory", "strict": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "runtime_env": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 25, "name": "runtime_env", "strict": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}}, "module_name": "src.data_trans.executor.ray_config", "mro": ["src.data_trans.executor.ray_config.RayClusterConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "address", "namespace", "local_mode", "runtime_env", "num_cpus", "num_gpus", "memory", "object_store_memory", "log_to_driver", "logging_level", "ignore_reinit_error", "include_dashboard", "dashboard_host", "dashboard_port"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "address", "namespace", "local_mode", "runtime_env", "num_cpus", "num_gpus", "memory", "object_store_memory", "log_to_driver", "logging_level", "ignore_reinit_error", "include_dashboard", "dashboard_host", "dashboard_port"], "arg_types": ["src.data_trans.executor.ray_config.RayClusterConfig", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayClusterConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.address", "name": "address", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "dashboard_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.dashboard_host", "name": "dashboard_host", "setter_type": null, "type": "builtins.str"}}, "dashboard_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.dashboard_port", "name": "dashboard_port", "setter_type": null, "type": "builtins.int"}}, "ignore_reinit_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.ignore_reinit_error", "name": "ignore_reinit_error", "setter_type": null, "type": "builtins.bool"}}, "include_dashboard": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.include_dashboard", "name": "include_dashboard", "setter_type": null, "type": "builtins.bool"}}, "local_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.local_mode", "name": "local_mode", "setter_type": null, "type": "builtins.bool"}}, "log_to_driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.log_to_driver", "name": "log_to_driver", "setter_type": null, "type": "builtins.bool"}}, "logging_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.logging_level", "name": "logging_level", "setter_type": null, "type": "builtins.str"}}, "memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.memory", "name": "memory", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "address", "namespace", "local_mode", "runtime_env", "num_cpus", "num_gpus", "memory", "object_store_memory", "log_to_driver", "logging_level", "ignore_reinit_error", "include_dashboard", "dashboard_host", "dashboard_port"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "address", "namespace", "local_mode", "runtime_env", "num_cpus", "num_gpus", "memory", "object_store_memory", "log_to_driver", "logging_level", "ignore_reinit_error", "include_dashboard", "dashboard_host", "dashboard_port"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_config.RayClusterConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayClusterConfig", "ret_type": "src.data_trans.executor.ray_config.RayClusterConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "address", "namespace", "local_mode", "runtime_env", "num_cpus", "num_gpus", "memory", "object_store_memory", "log_to_driver", "logging_level", "ignore_reinit_error", "include_dashboard", "dashboard_host", "dashboard_port"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_config.RayClusterConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayClusterConfig", "ret_type": "src.data_trans.executor.ray_config.RayClusterConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.namespace", "name": "namespace", "setter_type": null, "type": "builtins.str"}}, "num_cpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.num_cpus", "name": "num_cpus", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "num_gpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.num_gpus", "name": "num_gpus", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "object_store_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.object_store_memory", "name": "object_store_memory", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "runtime_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.runtime_env", "name": "runtime_env", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "setup_runtime_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "v"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.setup_runtime_env", "name": "setup_runtime_env", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.setup_runtime_env", "name": "setup_runtime_env", "setter_type": null, "type": {".class": "UnionType", "items": ["pydantic.deprecated.class_validators._OnlyValueValidatorClsMethod", "pydantic.deprecated.class_validators._V1ValidatorWithValuesClsMethod", "pydantic.deprecated.class_validators._V1ValidatorWithValuesKwOnlyClsMethod", "pydantic.deprecated.class_validators._V1ValidatorWithKwargsClsMethod", "pydantic.deprecated.class_validators._V1ValidatorWithValuesAndKwargsClsMethod", "pydantic._internal._decorators_v1.V1ValidatorWithValues", "pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly", "pydantic._internal._decorators_v1.V1ValidatorWithKwargs", "pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs"], "uses_pep604_syntax": false}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_config.RayClusterConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_config.RayClusterConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RayConfigManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_config.RayConfigManager", "name": "RayConfigManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayConfigManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.data_trans.executor.ray_config", "mro": ["src.data_trans.executor.ray_config.RayConfigManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayConfigManager.__init__", "name": "__init__", "type": null}}, "_actor_resource_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager._actor_resource_config", "name": "_actor_resource_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.executor.ray_config.RayActorResourceConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_cluster_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager._cluster_config", "name": "_cluster_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.executor.ray_config.RayClusterConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_create_actor_resource_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager._create_actor_resource_config", "name": "_create_actor_resource_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_config.RayConfigManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_actor_resource_config of RayConfigManager", "ret_type": "src.data_trans.executor.ray_config.RayActorResourceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_cluster_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager._create_cluster_config", "name": "_create_cluster_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_config.RayConfigManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_cluster_config of RayConfigManager", "ret_type": "src.data_trans.executor.ray_config.RayClusterConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_performance_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager._create_performance_config", "name": "_create_performance_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_config.RayConfigManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_performance_config of RayConfigManager", "ret_type": "src.data_trans.executor.ray_config.RayPerformanceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_task_resource_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager._create_task_resource_config", "name": "_create_task_resource_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_config.RayConfigManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_task_resource_config of RayConfigManager", "ret_type": "src.data_trans.executor.ray_config.RayTaskResourceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_performance_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager._performance_config", "name": "_performance_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.executor.ray_config.RayPerformanceConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_task_resource_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager._task_resource_config", "name": "_task_resource_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.executor.ray_config.RayTaskResourceConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_actor_resource_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager.get_actor_resource_config", "name": "get_actor_resource_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_config.RayConfigManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_actor_resource_config of RayConfigManager", "ret_type": "src.data_trans.executor.ray_config.RayActorResourceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cluster_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager.get_cluster_config", "name": "get_cluster_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_config.RayConfigManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cluster_config of RayConfigManager", "ret_type": "src.data_trans.executor.ray_config.RayClusterConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_performance_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager.get_performance_config", "name": "get_performance_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_config.RayConfigManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_performance_config of RayConfigManager", "ret_type": "src.data_trans.executor.ray_config.RayPerformanceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_ray_init_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager.get_ray_init_config", "name": "get_ray_init_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_config.RayConfigManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_ray_init_config of RayConfigManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_task_resource_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager.get_task_resource_config", "name": "get_task_resource_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_config.RayConfigManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_task_resource_config of RayConfigManager", "ret_type": "src.data_trans.executor.ray_config.RayTaskResourceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_config.RayConfigManager.settings", "name": "settings", "setter_type": null, "type": "src.data_trans.config.settings.AppConfig"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_config.RayConfigManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_config.RayConfigManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RayPerformanceConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig", "name": "RayPerformanceConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"gcs_rpc_server_reconnect_timeout_s": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 129, "name": "gcs_rpc_server_reconnect_timeout_s", "strict": null, "type": "builtins.int"}, "max_direct_call_object_size": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 145, "name": "max_direct_call_object_size", "strict": null, "type": "builtins.int"}, "max_pending_lease_requests_per_scheduling_category": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 119, "name": "max_pending_lease_requests_per_scheduling_category", "strict": null, "type": "builtins.int"}, "memory_monitor_refresh_ms": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 139, "name": "memory_monitor_refresh_ms", "strict": null, "type": "builtins.int"}, "object_store_memory": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 115, "name": "object_store_memory", "strict": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "plasma_directory": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 116, "name": "plasma_directory", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "raylet_heartbeat_timeout_milliseconds": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 124, "name": "raylet_heartbeat_timeout_milliseconds", "strict": null, "type": "builtins.int"}, "task_retry_delay_ms": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 144, "name": "task_retry_delay_ms", "strict": null, "type": "builtins.int"}, "worker_register_timeout_seconds": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 134, "name": "worker_register_timeout_seconds", "strict": null, "type": "builtins.int"}}}}, "module_name": "src.data_trans.executor.ray_config", "mro": ["src.data_trans.executor.ray_config.RayPerformanceConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "object_store_memory", "plasma_directory", "max_pending_lease_requests_per_scheduling_category", "raylet_heartbeat_timeout_milliseconds", "gcs_rpc_server_reconnect_timeout_s", "worker_register_timeout_seconds", "memory_monitor_refresh_ms", "task_retry_delay_ms", "max_direct_call_object_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "object_store_memory", "plasma_directory", "max_pending_lease_requests_per_scheduling_category", "raylet_heartbeat_timeout_milliseconds", "gcs_rpc_server_reconnect_timeout_s", "worker_register_timeout_seconds", "memory_monitor_refresh_ms", "task_retry_delay_ms", "max_direct_call_object_size"], "arg_types": ["src.data_trans.executor.ray_config.RayPerformanceConfig", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayPerformanceConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "gcs_rpc_server_reconnect_timeout_s": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.gcs_rpc_server_reconnect_timeout_s", "name": "gcs_rpc_server_reconnect_timeout_s", "setter_type": null, "type": "builtins.int"}}, "max_direct_call_object_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.max_direct_call_object_size", "name": "max_direct_call_object_size", "setter_type": null, "type": "builtins.int"}}, "max_pending_lease_requests_per_scheduling_category": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.max_pending_lease_requests_per_scheduling_category", "name": "max_pending_lease_requests_per_scheduling_category", "setter_type": null, "type": "builtins.int"}}, "memory_monitor_refresh_ms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.memory_monitor_refresh_ms", "name": "memory_monitor_refresh_ms", "setter_type": null, "type": "builtins.int"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "object_store_memory", "plasma_directory", "max_pending_lease_requests_per_scheduling_category", "raylet_heartbeat_timeout_milliseconds", "gcs_rpc_server_reconnect_timeout_s", "worker_register_timeout_seconds", "memory_monitor_refresh_ms", "task_retry_delay_ms", "max_direct_call_object_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "object_store_memory", "plasma_directory", "max_pending_lease_requests_per_scheduling_category", "raylet_heartbeat_timeout_milliseconds", "gcs_rpc_server_reconnect_timeout_s", "worker_register_timeout_seconds", "memory_monitor_refresh_ms", "task_retry_delay_ms", "max_direct_call_object_size"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_config.RayPerformanceConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayPerformanceConfig", "ret_type": "src.data_trans.executor.ray_config.RayPerformanceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "object_store_memory", "plasma_directory", "max_pending_lease_requests_per_scheduling_category", "raylet_heartbeat_timeout_milliseconds", "gcs_rpc_server_reconnect_timeout_s", "worker_register_timeout_seconds", "memory_monitor_refresh_ms", "task_retry_delay_ms", "max_direct_call_object_size"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_config.RayPerformanceConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayPerformanceConfig", "ret_type": "src.data_trans.executor.ray_config.RayPerformanceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "object_store_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.object_store_memory", "name": "object_store_memory", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "plasma_directory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.plasma_directory", "name": "plasma_directory", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "raylet_heartbeat_timeout_milliseconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.raylet_heartbeat_timeout_milliseconds", "name": "raylet_heartbeat_timeout_milliseconds", "setter_type": null, "type": "builtins.int"}}, "task_retry_delay_ms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.task_retry_delay_ms", "name": "task_retry_delay_ms", "setter_type": null, "type": "builtins.int"}}, "worker_register_timeout_seconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.worker_register_timeout_seconds", "name": "worker_register_timeout_seconds", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_config.RayPerformanceConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_config.RayPerformanceConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RayTaskResourceConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig", "name": "RayTaskResourceConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"max_retries": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 78, "name": "max_retries", "strict": null, "type": "builtins.int"}, "memory": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 69, "name": "memory", "strict": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "num_cpus": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 66, "name": "num_cpus", "strict": null, "type": "builtins.float"}, "num_gpus": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 72, "name": "num_gpus", "strict": null, "type": "builtins.float"}, "placement_group": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 83, "name": "placement_group", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "resources": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 75, "name": "resources", "strict": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}, "retry_exceptions": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 79, "name": "retry_exceptions", "strict": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, "scheduling_strategy": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 82, "name": "scheduling_strategy", "strict": null, "type": "builtins.str"}}}}, "module_name": "src.data_trans.executor.ray_config", "mro": ["src.data_trans.executor.ray_config.RayTaskResourceConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "num_cpus", "memory", "num_gpus", "resources", "max_retries", "retry_exceptions", "scheduling_strategy", "placement_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "num_cpus", "memory", "num_gpus", "resources", "max_retries", "retry_exceptions", "scheduling_strategy", "placement_group"], "arg_types": ["src.data_trans.executor.ray_config.RayTaskResourceConfig", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayTaskResourceConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "max_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.max_retries", "name": "max_retries", "setter_type": null, "type": "builtins.int"}}, "memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.memory", "name": "memory", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "num_cpus", "memory", "num_gpus", "resources", "max_retries", "retry_exceptions", "scheduling_strategy", "placement_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "num_cpus", "memory", "num_gpus", "resources", "max_retries", "retry_exceptions", "scheduling_strategy", "placement_group"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_config.RayTaskResourceConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayTaskResourceConfig", "ret_type": "src.data_trans.executor.ray_config.RayTaskResourceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "num_cpus", "memory", "num_gpus", "resources", "max_retries", "retry_exceptions", "scheduling_strategy", "placement_group"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_config.RayTaskResourceConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayTaskResourceConfig", "ret_type": "src.data_trans.executor.ray_config.RayTaskResourceConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "num_cpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.num_cpus", "name": "num_cpus", "setter_type": null, "type": "builtins.float"}}, "num_gpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.num_gpus", "name": "num_gpus", "setter_type": null, "type": "builtins.float"}}, "placement_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.placement_group", "name": "placement_group", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.resources", "name": "resources", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "retry_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.retry_exceptions", "name": "retry_exceptions", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "scheduling_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.scheduling_strategy", "name": "scheduling_strategy", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_config.RayTaskResourceConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_config.RayTaskResourceConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_config.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_config.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_config.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_config.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_config.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_config.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_config_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_config._config_manager", "name": "_config_manager", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.executor.ray_config.RayConfigManager", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_ray_config_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_config.get_ray_config_manager", "name": "get_ray_config_manager", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_ray_config_manager", "ret_type": "src.data_trans.executor.ray_config.RayConfigManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_settings": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.config.settings.get_settings", "kind": "Gdef", "module_hidden": true, "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.deprecated.class_validators.validator", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "D:\\github_projects\\data_trans\\src\\data_trans\\executor\\ray_config.py"}