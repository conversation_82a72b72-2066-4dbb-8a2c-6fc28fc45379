{".class": "MypyFile", "_fullname": "src.data_trans.executor.ray_executor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseExecutor": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.base_executor.BaseExecutor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExecutionResult": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.base_executor.ExecutionResult", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExecutionStatus": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.base_executor.ExecutionStatus", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExecutorConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.base_executor.ExecutorConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RAY_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RAY_AVAILABLE", "name": "RAY_AVAILABLE", "setter_type": null, "type": "builtins.bool"}}, "RayExecutor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.data_trans.executor.base_executor.BaseExecutor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_executor.RayExecutor", "name": "RayExecutor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_executor.RayExecutor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "src.data_trans.executor.ray_executor", "mro": ["src.data_trans.executor.ray_executor.RayExecutor", "src.data_trans.executor.base_executor.BaseExecutor", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor", "src.data_trans.executor.ray_executor.RayExecutorConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_execute_ray_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_config", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor._execute_ray_task", "name": "_execute_ray_task", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_config", "result"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "src.data_trans.executor.base_executor.ExecutionResult"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_execute_ray_task of RayExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialize_local_executor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor._initialize_local_executor", "name": "_initialize_local_executor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_initialize_local_executor of RayExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialize_ray": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor._initialize_ray", "name": "_initialize_ray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_initialize_ray of RayExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_local_executor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor._local_executor", "name": "_local_executor", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.executor.task_executor.TaskExecutor", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_ray_cluster_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor._ray_cluster_info", "name": "_ray_cluster_info", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_ray_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor._ray_initialized", "name": "_ray_initialized", "setter_type": null, "type": "builtins.bool"}}, "cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.cleanup", "name": "cleanup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cleanup of RayExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.config", "name": "config", "setter_type": null, "type": "src.data_trans.executor.ray_executor.RayExecutorConfig"}}, "execute_batch_distributed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tasks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.execute_batch_distributed", "name": "execute_batch_distributed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tasks"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute_batch_distributed of RayExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["src.data_trans.executor.base_executor.ExecutionResult"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_distributed_cleaning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "raw_data_batches", "clean_config", "task_id_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.execute_distributed_cleaning", "name": "execute_distributed_cleaning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "raw_data_batches", "clean_config", "task_id_prefix"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute_distributed_cleaning of RayExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["src.data_trans.executor.ray_executor.RayTaskResult"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_id", "task_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.execute_task", "name": "execute_task", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_id", "task_config"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute_task of RayExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.base_executor.ExecutionResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cluster_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.get_cluster_info", "name": "get_cluster_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cluster_info of RayExecutor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_ray_dashboard_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.get_ray_dashboard_url", "name": "get_ray_dashboard_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_ray_dashboard_url of RayExecutor", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scale_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target_workers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.scale_cluster", "name": "scale_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target_workers"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutor", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "scale_cluster of RayExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_executor.RayExecutor.settings", "name": "settings", "setter_type": null, "type": "src.data_trans.config.settings.AppConfig"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_executor.RayExecutor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_executor.RayExecutor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RayExecutorConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.data_trans.executor.task_executor.TaskExecutorConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig", "name": "RayExecutorConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"cleaned_data_collection": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 62, "name": "cleaned_data_collection", "strict": null, "type": "builtins.str"}, "default_cleaner_type": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 67, "name": "default_cleaner_type", "strict": null, "type": "builtins.str"}, "default_cpu_per_task": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 65, "name": "default_cpu_per_task", "strict": null, "type": "builtins.float"}, "default_memory_per_task": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 66, "name": "default_memory_per_task", "strict": null, "type": "builtins.int"}, "default_storage_type": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 60, "name": "default_storage_type", "strict": null, "type": "builtins.str"}, "enable_after_clean_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 73, "name": "enable_after_clean_hook", "strict": null, "type": "builtins.bool"}, "enable_after_crawl_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 71, "name": "enable_after_crawl_hook", "strict": null, "type": "builtins.bool"}, "enable_after_store_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 75, "name": "enable_after_store_hook", "strict": null, "type": "builtins.bool"}, "enable_before_clean_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 72, "name": "enable_before_clean_hook", "strict": null, "type": "builtins.bool"}, "enable_before_crawl_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 70, "name": "enable_before_crawl_hook", "strict": null, "type": "builtins.bool"}, "enable_before_store_hook": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 74, "name": "enable_before_store_hook", "strict": null, "type": "builtins.bool"}, "enable_hooks": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 98, "name": "enable_hooks", "strict": null, "type": "builtins.bool"}, "enable_metrics": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 95, "name": "enable_metrics", "strict": null, "type": "builtins.bool"}, "enable_ray_object_store": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 76, "name": "enable_ray_object_store", "strict": null, "type": "builtins.bool"}, "fallback_to_local": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 80, "name": "fallback_to_local", "strict": null, "type": "builtins.bool"}, "log_level": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 94, "name": "log_level", "strict": null, "type": "builtins.str"}, "max_concurrent_tasks": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 91, "name": "max_concurrent_tasks", "strict": null, "type": "builtins.int"}, "max_ray_workers": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 69, "name": "max_ray_workers", "strict": null, "type": "builtins.int"}, "max_retries": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 86, "name": "max_retries", "strict": null, "type": "builtins.int"}, "raw_data_collection": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 61, "name": "raw_data_collection", "strict": null, "type": "builtins.str"}, "ray_address": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 56, "name": "ray_address", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "ray_batch_size": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 77, "name": "ray_batch_size", "strict": null, "type": "builtins.int"}, "ray_namespace": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 62, "name": "ray_namespace", "strict": null, "type": "builtins.str"}, "ray_runtime_env": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 59, "name": "ray_runtime_env", "strict": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "ray_task_max_retries": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 72, "name": "ray_task_max_retries", "strict": null, "type": "builtins.int"}, "ray_task_retry_delay": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 73, "name": "ray_task_retry_delay", "strict": null, "type": "builtins.float"}, "retry_delay": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 87, "name": "retry_delay", "strict": null, "type": "builtins.float"}, "timeout": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 88, "name": "timeout", "strict": null, "type": "builtins.float"}}}}, "module_name": "src.data_trans.executor.ray_executor", "mro": ["src.data_trans.executor.ray_executor.RayExecutorConfig", "src.data_trans.executor.task_executor.TaskExecutorConfig", "src.data_trans.executor.base_executor.ExecutorConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "ray_address", "ray_runtime_env", "ray_namespace", "default_cpu_per_task", "default_memory_per_task", "max_ray_workers", "ray_task_max_retries", "ray_task_retry_delay", "enable_ray_object_store", "ray_batch_size", "fallback_to_local"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "ray_address", "ray_runtime_env", "ray_namespace", "default_cpu_per_task", "default_memory_per_task", "max_ray_workers", "ray_task_max_retries", "ray_task_retry_delay", "enable_ray_object_store", "ray_batch_size", "fallback_to_local"], "arg_types": ["src.data_trans.executor.ray_executor.RayExecutorConfig", "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.bool", "builtins.int", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayExecutorConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "cleaned_data_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.cleaned_data_collection", "name": "cleaned_data_collection", "setter_type": null, "type": "builtins.str"}}, "default_cleaner_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.default_cleaner_type", "name": "default_cleaner_type", "setter_type": null, "type": "builtins.str"}}, "default_cpu_per_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.default_cpu_per_task", "name": "default_cpu_per_task", "setter_type": null, "type": "builtins.float"}}, "default_memory_per_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.default_memory_per_task", "name": "default_memory_per_task", "setter_type": null, "type": "builtins.int"}}, "default_storage_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.default_storage_type", "name": "default_storage_type", "setter_type": null, "type": "builtins.str"}}, "enable_after_clean_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.enable_after_clean_hook", "name": "enable_after_clean_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_after_crawl_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.enable_after_crawl_hook", "name": "enable_after_crawl_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_after_store_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.enable_after_store_hook", "name": "enable_after_store_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_before_clean_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.enable_before_clean_hook", "name": "enable_before_clean_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_before_crawl_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.enable_before_crawl_hook", "name": "enable_before_crawl_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_before_store_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.enable_before_store_hook", "name": "enable_before_store_hook", "setter_type": null, "type": "builtins.bool"}}, "enable_hooks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.enable_hooks", "name": "enable_hooks", "setter_type": null, "type": "builtins.bool"}}, "enable_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.enable_metrics", "name": "enable_metrics", "setter_type": null, "type": "builtins.bool"}}, "enable_ray_object_store": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.enable_ray_object_store", "name": "enable_ray_object_store", "setter_type": null, "type": "builtins.bool"}}, "fallback_to_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.fallback_to_local", "name": "fallback_to_local", "setter_type": null, "type": "builtins.bool"}}, "log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.log_level", "name": "log_level", "setter_type": null, "type": "builtins.str"}}, "max_concurrent_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.max_concurrent_tasks", "name": "max_concurrent_tasks", "setter_type": null, "type": "builtins.int"}}, "max_ray_workers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.max_ray_workers", "name": "max_ray_workers", "setter_type": null, "type": "builtins.int"}}, "max_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.max_retries", "name": "max_retries", "setter_type": null, "type": "builtins.int"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "ray_address", "ray_runtime_env", "ray_namespace", "default_cpu_per_task", "default_memory_per_task", "max_ray_workers", "ray_task_max_retries", "ray_task_retry_delay", "enable_ray_object_store", "ray_batch_size", "fallback_to_local"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "ray_address", "ray_runtime_env", "ray_namespace", "default_cpu_per_task", "default_memory_per_task", "max_ray_workers", "ray_task_max_retries", "ray_task_retry_delay", "enable_ray_object_store", "ray_batch_size", "fallback_to_local"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_executor.RayExecutorConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.bool", "builtins.int", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayExecutorConfig", "ret_type": "src.data_trans.executor.ray_executor.RayExecutorConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "max_retries", "retry_delay", "timeout", "max_concurrent_tasks", "log_level", "enable_metrics", "enable_hooks", "default_storage_type", "raw_data_collection", "cleaned_data_collection", "default_cleaner_type", "enable_before_crawl_hook", "enable_after_crawl_hook", "enable_before_clean_hook", "enable_after_clean_hook", "enable_before_store_hook", "enable_after_store_hook", "ray_address", "ray_runtime_env", "ray_namespace", "default_cpu_per_task", "default_memory_per_task", "max_ray_workers", "ray_task_max_retries", "ray_task_retry_delay", "enable_ray_object_store", "ray_batch_size", "fallback_to_local"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_executor.RayExecutorConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.bool", "builtins.int", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayExecutorConfig", "ret_type": "src.data_trans.executor.ray_executor.RayExecutorConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "raw_data_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.raw_data_collection", "name": "raw_data_collection", "setter_type": null, "type": "builtins.str"}}, "ray_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.ray_address", "name": "ray_address", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ray_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.ray_batch_size", "name": "ray_batch_size", "setter_type": null, "type": "builtins.int"}}, "ray_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.ray_namespace", "name": "ray_namespace", "setter_type": null, "type": "builtins.str"}}, "ray_runtime_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.ray_runtime_env", "name": "ray_runtime_env", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ray_task_max_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.ray_task_max_retries", "name": "ray_task_max_retries", "setter_type": null, "type": "builtins.int"}}, "ray_task_retry_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.ray_task_retry_delay", "name": "ray_task_retry_delay", "setter_type": null, "type": "builtins.float"}}, "retry_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.retry_delay", "name": "retry_delay", "setter_type": null, "type": "builtins.float"}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.timeout", "name": "timeout", "setter_type": null, "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_executor.RayExecutorConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_executor.RayExecutorConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RayTaskResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.ray_executor.RayTaskResult", "name": "RayTaskResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"error": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 91, "name": "error", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "execution_time": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 92, "name": "execution_time", "strict": null, "type": "builtins.float"}, "result": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 90, "name": "result", "strict": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, "success": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 89, "name": "success", "strict": null, "type": "builtins.bool"}, "task_id": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 88, "name": "task_id", "strict": null, "type": "builtins.str"}, "worker_id": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 93, "name": "worker_id", "strict": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}}, "module_name": "src.data_trans.executor.ray_executor", "mro": ["src.data_trans.executor.ray_executor.RayTaskResult", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 3, 5], "arg_names": ["__pydantic_self__", "task_id", "success", "result", "error", "execution_time", "worker_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 3, 5], "arg_names": ["__pydantic_self__", "task_id", "success", "result", "error", "execution_time", "worker_id"], "arg_types": ["src.data_trans.executor.ray_executor.RayTaskResult", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RayTaskResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.error", "name": "error", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "execution_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.execution_time", "name": "execution_time", "setter_type": null, "type": "builtins.float"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 3, 5, 5, 3, 5], "arg_names": [null, "_fields_set", "task_id", "success", "result", "error", "execution_time", "worker_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 5, 5, 3, 5], "arg_names": ["_cls", "_fields_set", "task_id", "success", "result", "error", "execution_time", "worker_id"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_executor.RayTaskResult"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayTaskResult", "ret_type": "src.data_trans.executor.ray_executor.RayTaskResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 5, 5, 3, 5], "arg_names": ["_cls", "_fields_set", "task_id", "success", "result", "error", "execution_time", "worker_id"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.ray_executor.RayTaskResult"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of RayTaskResult", "ret_type": "src.data_trans.executor.ray_executor.RayTaskResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.result", "name": "result", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.success", "name": "success", "setter_type": null, "type": "builtins.bool"}}, "task_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.task_id", "name": "task_id", "setter_type": null, "type": "builtins.str"}}, "worker_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.worker_id", "name": "worker_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.ray_executor.RayTaskResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.ray_executor.RayTaskResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TaskConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskExecutor": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskExecutor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskExecutorConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.executor.task_executor.TaskExecutorConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.ray_executor.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.ray_executor.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "get_settings": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.config.settings.get_settings", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.data_trans.executor.ray_executor.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_executor.structlog", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_executor.structlog", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ray": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_executor.ray", "name": "ray", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_executor.ray", "source_any": null, "type_of_any": 3}}}, "remote": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_executor.remote", "name": "remote", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_executor.remote", "source_any": null, "type_of_any": 3}}}, "remote_clean_task": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["raw_data", "clean_config", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "src.data_trans.executor.ray_executor.remote_clean_task", "name": "remote_clean_task", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["raw_data", "clean_config", "task_id"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remote_clean_task", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.ray_executor.RayTaskResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_executor.remote_clean_task", "name": "remote_clean_task", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_executor.remote", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_executor.remote", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "remote_crawl_task": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["task_config", "settings_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "src.data_trans.executor.ray_executor.remote_crawl_task", "name": "remote_crawl_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["task_config", "settings_dict"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remote_crawl_task", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.ray_executor.RayTaskResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_executor.remote_crawl_task", "name": "remote_crawl_task", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_executor.remote", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_executor.remote", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "structlog": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.ray_executor.structlog", "name": "structlog", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.ray_executor.structlog", "source_any": null, "type_of_any": 3}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "src\\data_trans\\executor\\ray_executor.py"}