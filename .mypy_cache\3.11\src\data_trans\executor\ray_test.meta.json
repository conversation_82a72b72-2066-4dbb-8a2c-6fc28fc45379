{"data_mtime": 1752392499, "dep_lines": [15, 16, 149, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1, 13], "dep_prios": [5, 5, 20, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 10], "dependencies": ["src.data_trans.executor.ray_config", "src.data_trans.executor.ray_executor", "src.data_trans.executor.ray_actors", "asyncio", "json", "logging", "datetime", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "os"], "hash": "a0dea383fae70893eb654ecd26a8385565d0cbeb", "id": "src.data_trans.executor.ray_test", "ignore_all": false, "interface_hash": "b20729eb79399f8822e38483789a19ab1dd225aa", "mtime": 1752392497, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src\\data_trans\\executor\\ray_test.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 8213, "suppressed": ["structlog"], "version_id": "1.16.1"}