{"data_mtime": 1752391815, "dep_lines": [16, 17, 18, 19, 20, 21, 22, 7, 8, 9, 10, 11, 12, 1, 1, 1, 1, 14], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 10], "dependencies": ["src.data_trans.executor.python311_compatibility", "src.data_trans.executor.ray_config", "src.data_trans.executor.ray_executor", "src.data_trans.executor.ray_task_executor", "src.data_trans.executor.ray_resource_manager", "src.data_trans.executor.ray_cluster_manager", "src.data_trans.executor.ray_state_manager", "asyncio", "json", "logging", "time", "datetime", "typing", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "b99ab1e4d9936f6e12554b386463a771be0e534f", "id": "src.data_trans.executor.ray_integration_test", "ignore_all": false, "interface_hash": "6eeb1e8e9ce11459b6a91b1142413ae3eefa1b79", "mtime": 1752391089, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "D:\\github_projects\\data_trans\\src\\data_trans\\executor\\ray_integration_test.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 16259, "suppressed": ["structlog"], "version_id": "1.16.1"}