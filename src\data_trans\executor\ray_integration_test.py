"""
Ray分布式计算集成测试套件

综合测试Ray集成的所有功能，验证Python 3.11兼容性和性能。
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog

from .python311_compatibility import run_python311_validation
from .ray_cluster_manager import Ray<PERSON>lusterManager
from .ray_config import get_ray_config_manager
from .ray_executor import RayExecutor, RayExecutorConfig
from .ray_resource_manager import RayResourceManager
from .ray_state_manager import RayStateManager
from .ray_task_executor import RayTaskExecutor, RayTaskExecutorConfig

logger = structlog.get_logger(__name__)


class RayIntegrationTestSuite:
    """Ray集成测试套件"""

    def __init__(self):
        """初始化测试套件"""
        self.test_results: Dict[str, Any] = {}
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None

        logger.info("Ray集成测试套件初始化完成")

    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试

        Returns:
            测试结果汇总
        """
        self.start_time = datetime.utcnow()
        logger.info("开始运行Ray集成测试套件")

        # 测试列表
        tests = [
            ("Python 3.11兼容性验证", self._test_python311_compatibility),
            ("Ray配置管理测试", self._test_ray_config),
            ("Ray执行器测试", self._test_ray_executor),
            ("Ray任务执行器测试", self._test_ray_task_executor),
            ("Ray资源管理测试", self._test_ray_resource_manager),
            ("Ray集群管理测试", self._test_ray_cluster_manager),
            ("Ray状态管理测试", self._test_ray_state_manager),
            ("Ray性能基准测试", self._test_ray_performance),
            ("Ray故障恢复测试", self._test_ray_fault_tolerance),
        ]

        # 执行测试
        for test_name, test_func in tests:
            logger.info(f"运行测试: {test_name}")
            try:
                result = await test_func()
                self.test_results[test_name] = {
                    "status": "passed" if result.get("success", False) else "failed",
                    "result": result,
                    "timestamp": datetime.utcnow().isoformat(),
                }
                logger.info(
                    f"测试完成: {test_name} - {self.test_results[test_name]['status']}"
                )
            except Exception as e:
                self.test_results[test_name] = {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat(),
                }
                logger.error(f"测试异常: {test_name}", error=str(e))

        self.end_time = datetime.utcnow()

        # 生成汇总报告
        return self._generate_summary_report()

    async def _test_python311_compatibility(self) -> Dict[str, Any]:
        """测试Python 3.11兼容性"""
        try:
            report = await run_python311_validation()

            # 检查关键特性
            feature_score = report["feature_support"]["score"]
            is_compatible = report["is_python311_plus"]

            return {
                "success": is_compatible and feature_score > 70,
                "feature_score": feature_score,
                "is_python311": is_compatible,
                "details": report,
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_ray_config(self) -> Dict[str, Any]:
        """测试Ray配置管理"""
        try:
            config_manager = get_ray_config_manager()

            # 测试各种配置获取
            cluster_config = config_manager.get_cluster_config()
            task_config = config_manager.get_task_resource_config()
            actor_config = config_manager.get_actor_resource_config()
            performance_config = config_manager.get_performance_config()
            init_config = config_manager.get_ray_init_config()

            return {
                "success": True,
                "cluster_config": cluster_config.model_dump(),
                "task_config": task_config.model_dump(),
                "actor_config": actor_config.model_dump(),
                "performance_config": performance_config.model_dump(),
                "init_config_keys": list(init_config.keys()),
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_ray_executor(self) -> Dict[str, Any]:
        """测试Ray执行器"""
        try:
            config = RayExecutorConfig(
                max_concurrent_tasks=2, timeout=30.0, fallback_to_local=True
            )

            executor = RayExecutor(config)

            # 等待初始化
            await asyncio.sleep(2)

            # 获取集群信息
            cluster_info = executor.get_cluster_info()

            # 测试简单任务执行
            task_config = {
                "task_id": "test_ray_executor",
                "task_type": "crawl_clean_store",
                "crawler_type": "api",
                "urls": ["https://httpbin.org/json"],
                "crawler_config": {"timeout": 10.0},
                "cleaner_type": "text",
                "storage_type": "redis",
            }

            result = await executor.execute_task("test_ray_executor", task_config)

            await executor.cleanup()

            return {
                "success": True,
                "cluster_info": cluster_info,
                "task_result": {
                    "status": result.status.value,
                    "duration": result.duration,
                    "logs_count": len(result.logs),
                },
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_ray_task_executor(self) -> Dict[str, Any]:
        """测试Ray任务执行器"""
        try:
            config = RayTaskExecutorConfig(
                enable_ray_execution=True,
                ray_fallback_to_local=True,
                ray_task_timeout=30.0,
            )

            executor = RayTaskExecutor(config)

            # 测试Ray状态
            ray_status = executor.get_ray_status()

            # 测试任务执行
            task_config = {
                "task_id": "test_ray_task_executor",
                "urls": ["https://httpbin.org/delay/1"],
                "crawler_type": "api",
                "cleaner_type": "text",
                "storage_type": "redis",
            }

            result = await executor.execute_task("test_ray_task_executor", task_config)

            await executor.cleanup()

            return {
                "success": True,
                "ray_status": ray_status,
                "task_result": {
                    "status": result.status.value,
                    "duration": result.duration,
                },
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_ray_resource_manager(self) -> Dict[str, Any]:
        """测试Ray资源管理"""
        try:
            resource_manager = RayResourceManager()

            # 获取当前资源使用情况
            current_usage = resource_manager.get_current_usage()

            # 计算资源分配
            allocation = resource_manager.calculate_optimal_allocation(
                task_count=5, task_requirements={"cpu": 1.0, "memory": 512, "gpu": 0.0}
            )

            # 获取使用统计
            stats = resource_manager.get_usage_statistics(hours=1)

            return {
                "success": True,
                "current_usage": current_usage.model_dump() if current_usage else None,
                "allocation": allocation,
                "stats": stats,
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_ray_cluster_manager(self) -> Dict[str, Any]:
        """测试Ray集群管理"""
        try:
            cluster_manager = RayClusterManager()

            # 初始化集群
            init_success = await cluster_manager.initialize_cluster()

            if init_success:
                # 检查集群健康
                health = await cluster_manager.check_cluster_health()

                # 获取集群信息
                cluster_info = cluster_manager.get_cluster_info()

                # 关闭集群
                await cluster_manager.shutdown_cluster()

                return {
                    "success": True,
                    "init_success": init_success,
                    "health": health.model_dump(),
                    "cluster_info": cluster_info,
                }
            else:
                return {"success": False, "error": "集群初始化失败"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_ray_state_manager(self) -> Dict[str, Any]:
        """测试Ray状态管理"""
        try:
            state_manager = RayStateManager("test_state_manager")

            # 测试状态操作
            await state_manager.set("test_key", "test_value", ttl=60)
            value = await state_manager.get("test_key")
            exists = await state_manager.exists("test_key")
            keys = await state_manager.keys()

            # 创建快照
            snapshot_id = await state_manager.create_snapshot()

            # 获取统计信息
            stats = await state_manager.get_statistics()

            # 清理
            await state_manager.delete("test_key")

            return {
                "success": True,
                "value_retrieved": value == "test_value",
                "exists_check": exists,
                "keys_count": len(keys),
                "snapshot_created": bool(snapshot_id),
                "stats": stats,
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_ray_performance(self) -> Dict[str, Any]:
        """测试Ray性能"""
        try:
            # 创建性能测试任务
            task_count = 10
            tasks = []

            for i in range(task_count):
                task = {
                    "task_id": f"perf_test_{i}",
                    "urls": [f"https://httpbin.org/delay/{i%3+1}"],
                    "crawler_type": "api",
                    "cleaner_type": "text",
                    "storage_type": "redis",
                }
                tasks.append(task)

            # 测试批量执行性能
            config = RayTaskExecutorConfig(
                enable_ray_execution=True,
                ray_fallback_to_local=True,
                ray_enable_batching=True,
            )

            executor = RayTaskExecutor(config)

            start_time = time.time()
            results = await executor.execute_batch(tasks)
            end_time = time.time()

            await executor.cleanup()

            # 分析结果
            success_count = sum(1 for r in results if r.status.value == "success")
            total_time = end_time - start_time
            avg_time_per_task = total_time / len(results) if results else 0

            return {
                "success": True,
                "task_count": task_count,
                "success_count": success_count,
                "total_time": total_time,
                "avg_time_per_task": avg_time_per_task,
                "throughput": len(results) / total_time if total_time > 0 else 0,
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_ray_fault_tolerance(self) -> Dict[str, Any]:
        """测试Ray故障容错"""
        try:
            # 这里测试基本的错误处理能力
            config = RayTaskExecutorConfig(
                ray_fallback_to_local=True, ray_max_retries=2
            )

            executor = RayTaskExecutor(config)

            # 测试无效任务配置
            invalid_task = {
                "task_id": "fault_test",
                "urls": ["invalid_url"],
                "crawler_type": "invalid_type",
            }

            result = await executor.execute_task("fault_test", invalid_task)

            await executor.cleanup()

            # 应该失败但不崩溃
            return {
                "success": True,
                "handled_invalid_task": result.status.value in ["failed", "timeout"],
                "error_message": result.error_message,
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _generate_summary_report(self) -> Dict[str, Any]:
        """生成汇总报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(
            1 for r in self.test_results.values() if r["status"] == "passed"
        )
        failed_tests = sum(
            1 for r in self.test_results.values() if r["status"] == "failed"
        )
        error_tests = sum(
            1 for r in self.test_results.values() if r["status"] == "error"
        )

        duration = (
            (self.end_time - self.start_time).total_seconds()
            if self.start_time and self.end_time
            else 0
        )

        return {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "errors": error_tests,
                "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
                "duration_seconds": duration,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "end_time": self.end_time.isoformat() if self.end_time else None,
            },
            "test_results": self.test_results,
            "recommendations": self._generate_recommendations(),
        }

    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []

        # 分析测试结果并生成建议
        python311_result = self.test_results.get("Python 3.11兼容性验证")
        if python311_result and python311_result["status"] == "passed":
            feature_score = python311_result["result"].get("feature_score", 0)
            if feature_score < 80:
                recommendations.append("建议充分利用Python 3.11的新特性来提升性能")

        ray_executor_result = self.test_results.get("Ray执行器测试")
        if ray_executor_result and ray_executor_result["status"] == "failed":
            recommendations.append("Ray执行器存在问题，建议检查Ray集群配置")

        performance_result = self.test_results.get("Ray性能基准测试")
        if performance_result and performance_result["status"] == "passed":
            throughput = performance_result["result"].get("throughput", 0)
            if throughput < 1.0:
                recommendations.append(
                    "Ray任务执行吞吐量较低，建议优化任务配置或增加资源"
                )

        return recommendations


async def run_ray_integration_tests() -> Dict[str, Any]:
    """运行Ray集成测试

    Returns:
        测试结果报告
    """
    test_suite = RayIntegrationTestSuite()
    return await test_suite.run_all_tests()


if __name__ == "__main__":
    asyncio.run(run_ray_integration_tests())
