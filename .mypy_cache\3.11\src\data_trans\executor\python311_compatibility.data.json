{".class": "MypyFile", "_fullname": "src.data_trans.executor.python311_compatibility", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PerformanceBenchmark": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark", "name": "PerformanceBenchmark", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"async_completion_time": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 55, "name": "async_completion_time", "strict": null, "type": "builtins.float"}, "async_task_count": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 54, "name": "async_task_count", "strict": null, "type": "builtins.int"}, "cpu_usage": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 51, "name": "cpu_usage", "strict": null, "type": "builtins.float"}, "exception_handling_time": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 58, "name": "exception_handling_time", "strict": null, "type": "builtins.float"}, "execution_time": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 49, "name": "execution_time", "strict": null, "type": "builtins.float"}, "gc_collections": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 61, "name": "gc_collections", "strict": null, "type": "builtins.int"}, "memory_peak": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 62, "name": "memory_peak", "strict": null, "type": "builtins.int"}, "memory_usage": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 50, "name": "memory_usage", "strict": null, "type": "builtins.int"}, "test_name": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 48, "name": "test_name", "strict": null, "type": "builtins.str"}}}}, "module_name": "src.data_trans.executor.python311_compatibility", "mro": ["src.data_trans.executor.python311_compatibility.PerformanceBenchmark", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "test_name", "execution_time", "memory_usage", "cpu_usage", "async_task_count", "async_completion_time", "exception_handling_time", "gc_collections", "memory_peak"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "test_name", "execution_time", "memory_usage", "cpu_usage", "async_task_count", "async_completion_time", "exception_handling_time", "gc_collections", "memory_peak"], "arg_types": ["src.data_trans.executor.python311_compatibility.PerformanceBenchmark", "builtins.str", "builtins.float", "builtins.int", "builtins.float", "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PerformanceBenchmark", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "async_completion_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.async_completion_time", "name": "async_completion_time", "setter_type": null, "type": "builtins.float"}}, "async_task_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.async_task_count", "name": "async_task_count", "setter_type": null, "type": "builtins.int"}}, "cpu_usage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.cpu_usage", "name": "cpu_usage", "setter_type": null, "type": "builtins.float"}}, "exception_handling_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.exception_handling_time", "name": "exception_handling_time", "setter_type": null, "type": "builtins.float"}}, "execution_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.execution_time", "name": "execution_time", "setter_type": null, "type": "builtins.float"}}, "gc_collections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.gc_collections", "name": "gc_collections", "setter_type": null, "type": "builtins.int"}}, "memory_peak": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.memory_peak", "name": "memory_peak", "setter_type": null, "type": "builtins.int"}}, "memory_usage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.memory_usage", "name": "memory_usage", "setter_type": null, "type": "builtins.int"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 3, 3, 3, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "test_name", "execution_time", "memory_usage", "cpu_usage", "async_task_count", "async_completion_time", "exception_handling_time", "gc_collections", "memory_peak"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 3, 3, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "test_name", "execution_time", "memory_usage", "cpu_usage", "async_task_count", "async_completion_time", "exception_handling_time", "gc_collections", "memory_peak"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.float", "builtins.int", "builtins.float", "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of PerformanceBenchmark", "ret_type": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 3, 3, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "test_name", "execution_time", "memory_usage", "cpu_usage", "async_task_count", "async_completion_time", "exception_handling_time", "gc_collections", "memory_peak"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.float", "builtins.int", "builtins.float", "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of PerformanceBenchmark", "ret_type": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "test_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.test_name", "name": "test_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.python311_compatibility.PerformanceBenchmark", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Python311Features": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.python311_compatibility.Python311Features", "name": "Python311Features", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"dataclass_transforms": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 34, "name": "dataclass_transforms", "strict": null, "type": "builtins.bool"}, "exception_groups": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 28, "name": "exception_groups", "strict": null, "type": "builtins.bool"}, "execution_speed_improvement": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 42, "name": "execution_speed_improvement", "strict": null, "type": "builtins.float"}, "faster_cpython": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 27, "name": "faster_cpython", "strict": null, "type": "builtins.bool"}, "fine_grained_error_locations": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 38, "name": "fine_grained_error_locations", "strict": null, "type": "builtins.bool"}, "literal_string": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 33, "name": "literal_string", "strict": null, "type": "builtins.bool"}, "self_type": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 32, "name": "self_type", "strict": null, "type": "builtins.bool"}, "startup_time_improvement": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 41, "name": "startup_time_improvement", "strict": null, "type": "builtins.float"}, "task_groups": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 29, "name": "task_groups", "strict": null, "type": "builtins.bool"}, "tomllib": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 37, "name": "<PERSON><PERSON><PERSON><PERSON>", "strict": null, "type": "builtins.bool"}, "version": {"alias": null, "column": 4, "has_default": false, "has_dynamic_alias": false, "is_frozen": false, "line": 24, "name": "version", "strict": null, "type": "builtins.str"}}}}, "module_name": "src.data_trans.executor.python311_compatibility", "mro": ["src.data_trans.executor.python311_compatibility.Python311Features", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["__pydantic_self__", "version", "faster_cpython", "exception_groups", "task_groups", "self_type", "literal_string", "dataclass_transforms", "<PERSON><PERSON><PERSON><PERSON>", "fine_grained_error_locations", "startup_time_improvement", "execution_speed_improvement"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["__pydantic_self__", "version", "faster_cpython", "exception_groups", "task_groups", "self_type", "literal_string", "dataclass_transforms", "<PERSON><PERSON><PERSON><PERSON>", "fine_grained_error_locations", "startup_time_improvement", "execution_speed_improvement"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Features", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Python311Features", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "dataclass_transforms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.dataclass_transforms", "name": "dataclass_transforms", "setter_type": null, "type": "builtins.bool"}}, "exception_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.exception_groups", "name": "exception_groups", "setter_type": null, "type": "builtins.bool"}}, "execution_speed_improvement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.execution_speed_improvement", "name": "execution_speed_improvement", "setter_type": null, "type": "builtins.float"}}, "faster_cpython": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.faster_cpython", "name": "faster_cpython", "setter_type": null, "type": "builtins.bool"}}, "fine_grained_error_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.fine_grained_error_locations", "name": "fine_grained_error_locations", "setter_type": null, "type": "builtins.bool"}}, "literal_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.literal_string", "name": "literal_string", "setter_type": null, "type": "builtins.bool"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": [null, "_fields_set", "version", "faster_cpython", "exception_groups", "task_groups", "self_type", "literal_string", "dataclass_transforms", "<PERSON><PERSON><PERSON><PERSON>", "fine_grained_error_locations", "startup_time_improvement", "execution_speed_improvement"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["_cls", "_fields_set", "version", "faster_cpython", "exception_groups", "task_groups", "self_type", "literal_string", "dataclass_transforms", "<PERSON><PERSON><PERSON><PERSON>", "fine_grained_error_locations", "startup_time_improvement", "execution_speed_improvement"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.python311_compatibility.Python311Features"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of Python311Features", "ret_type": "src.data_trans.executor.python311_compatibility.Python311Features", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["_cls", "_fields_set", "version", "faster_cpython", "exception_groups", "task_groups", "self_type", "literal_string", "dataclass_transforms", "<PERSON><PERSON><PERSON><PERSON>", "fine_grained_error_locations", "startup_time_improvement", "execution_speed_improvement"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.executor.python311_compatibility.Python311Features"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of Python311Features", "ret_type": "src.data_trans.executor.python311_compatibility.Python311Features", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "self_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.self_type", "name": "self_type", "setter_type": null, "type": "builtins.bool"}}, "startup_time_improvement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.startup_time_improvement", "name": "startup_time_improvement", "setter_type": null, "type": "builtins.float"}}, "task_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.task_groups", "name": "task_groups", "setter_type": null, "type": "builtins.bool"}}, "tomllib": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.tomllib", "name": "<PERSON><PERSON><PERSON><PERSON>", "setter_type": null, "type": "builtins.bool"}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.version", "name": "version", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.python311_compatibility.Python311Features.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.python311_compatibility.Python311Features", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Python311Validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator", "name": "Python311Validator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.data_trans.executor.python311_compatibility", "mro": ["src.data_trans.executor.python311_compatibility.Python311Validator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator.__init__", "name": "__init__", "type": null}}, "_benchmark_async_performance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._benchmark_async_performance", "name": "_benchmark_async_performance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_benchmark_async_performance of Python311Validator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.python311_compatibility.PerformanceBenchmark"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_benchmark_exception_handling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._benchmark_exception_handling", "name": "_benchmark_exception_handling", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_benchmark_exception_handling of Python311Validator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.python311_compatibility.PerformanceBenchmark"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_benchmark_memory_management": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._benchmark_memory_management", "name": "_benchmark_memory_management", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_benchmark_memory_management of Python311Validator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.python311_compatibility.PerformanceBenchmark"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_benchmark_typing_performance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._benchmark_typing_performance", "name": "_benchmark_typing_performance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_benchmark_typing_performance of Python311Validator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.executor.python311_compatibility.PerformanceBenchmark"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_dataclass_transforms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._check_dataclass_transforms", "name": "_check_dataclass_transforms", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_dataclass_transforms of Python311Validator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_exception_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._check_exception_groups", "name": "_check_exception_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_exception_groups of Python311Validator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_faster_cpython": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._check_faster_cpython", "name": "_check_faster_cpython", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_faster_cpython of Python311Validator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_fine_grained_errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._check_fine_grained_errors", "name": "_check_fine_grained_errors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_fine_grained_errors of Python311Validator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_literal_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._check_literal_string", "name": "_check_literal_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_literal_string of Python311Validator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_self_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._check_self_type", "name": "_check_self_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_self_type of Python311Validator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_task_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._check_task_groups", "name": "_check_task_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_task_groups of Python311Validator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_tomllib": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._check_tomllib", "name": "_check_tom<PERSON>b", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_tomllib of Python311Validator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_recommendations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "features", "benchmarks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._generate_recommendations", "name": "_generate_recommendations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "features", "benchmarks"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator", "src.data_trans.executor.python311_compatibility.Python311Features", {".class": "Instance", "args": ["src.data_trans.executor.python311_compatibility.PerformanceBenchmark"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_generate_recommendations of Python311Validator", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_memory_usage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._get_memory_usage", "name": "_get_memory_usage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_memory_usage of Python311Validator", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_measure_execution_improvement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._measure_execution_improvement", "name": "_measure_execution_improvement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_measure_execution_improvement of Python311Validator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_measure_startup_improvement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator._measure_startup_improvement", "name": "_measure_startup_improvement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_measure_startup_improvement of Python311Validator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_python311_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator.check_python311_features", "name": "check_python311_features", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_python311_features of Python311Validator", "ret_type": "src.data_trans.executor.python311_compatibility.Python311Features", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_compatibility_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "features", "benchmarks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator.generate_compatibility_report", "name": "generate_compatibility_report", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "features", "benchmarks"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator", "src.data_trans.executor.python311_compatibility.Python311Features", {".class": "Instance", "args": ["src.data_trans.executor.python311_compatibility.PerformanceBenchmark"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_compatibility_report of Python311Validator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_python311": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator.is_python311", "name": "is_python311", "setter_type": null, "type": "builtins.bool"}}, "python_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator.python_version", "name": "python_version", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "sys._version_info"}}}, "run_performance_benchmarks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator.run_performance_benchmarks", "name": "run_performance_benchmarks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.executor.python311_compatibility.Python311Validator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run_performance_benchmarks of Python311Validator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["src.data_trans.executor.python311_compatibility.PerformanceBenchmark"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.executor.python311_compatibility.Python311Validator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.executor.python311_compatibility.Python311Validator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.python311_compatibility.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.python311_compatibility.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.python311_compatibility.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.python311_compatibility.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.python311_compatibility.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.executor.python311_compatibility.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "gc": {".class": "SymbolTableNode", "cross_ref": "gc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.data_trans.executor.python311_compatibility.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.python311_compatibility.structlog", "source_any": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.python311_compatibility.structlog", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "run_python311_validation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "src.data_trans.executor.python311_compatibility.run_python311_validation", "name": "run_python311_validation", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run_python311_validation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "structlog": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.executor.python311_compatibility.structlog", "name": "structlog", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.executor.python311_compatibility.structlog", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "D:\\github_projects\\data_trans\\src\\data_trans\\executor\\python311_compatibility.py"}