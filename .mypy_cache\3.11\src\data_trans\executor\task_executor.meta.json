{"data_mtime": 1752392499, "dep_lines": [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 7, 8, 9, 10, 11, 12, 13, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 15, 582], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20], "dependencies": ["src.data_trans.cleaners.base_cleaner", "src.data_trans.cleaners.text_cleaner", "src.data_trans.config.settings", "src.data_trans.crawlers.api_crawler", "src.data_trans.crawlers.base_crawler", "src.data_trans.crawlers.web_crawler", "src.data_trans.storage.base_storage", "src.data_trans.storage.mongodb_storage", "src.data_trans.storage.redis_storage", "src.data_trans.executor.base_executor", "asyncio", "json", "logging", "time", "datetime", "pathlib", "typing", "pydantic", "builtins", "_asyncio", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "contextlib", "enum", "os", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.types", "re", "src.data_trans.cleaners", "src.data_trans.config", "src.data_trans.crawlers", "src.data_trans.storage", "types", "typing_extensions"], "hash": "47a7c3ca1b3d1dbc87ad506ee4aab102d5ecb229", "id": "src.data_trans.executor.task_executor", "ignore_all": false, "interface_hash": "111b80572829299670a376b8fa1aa6b362c5739e", "mtime": 1752392497, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src\\data_trans\\executor\\task_executor.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 23075, "suppressed": ["structlog", "yaml"], "version_id": "1.16.1"}